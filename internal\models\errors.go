package models

import "errors"

// 通用错误
var (
	ErrNotFound     = errors.New("resource not found")
	ErrAlreadyExists = errors.New("resource already exists")
	ErrInvalidInput = errors.New("invalid input")
	ErrUnauthorized = errors.New("unauthorized")
	ErrForbidden    = errors.New("forbidden")
)

// MessageHandler相关错误
var (
	ErrInvalidHandlerName    = errors.New("invalid handler name")
	ErrEmptyMqNames         = errors.New("mq names cannot be empty")
	ErrEmptyCode            = errors.New("code cannot be empty")
	ErrEmptyDataProxyNames  = errors.New("data proxy names cannot be empty")
	ErrHandlerNotFound      = errors.New("message handler not found")
	ErrHandlerAlreadyExists = errors.New("message handler already exists")
	ErrHandlerDisabled      = errors.New("message handler is disabled")
)

// DataProxy相关错误
var (
	ErrMissingDSN           = errors.New("missing dsn in config")
	ErrMissingTable         = errors.New("missing table in config")
	ErrMissingURL           = errors.New("missing url in config")
	ErrProxyNotFound        = errors.New("data proxy not found")
	ErrProxyAlreadyExists   = errors.New("data proxy already exists")
	ErrProxyDisabled        = errors.New("data proxy is disabled")
	ErrUnsupportedProxyType = errors.New("unsupported proxy type")
)

// JavaScript执行相关错误
var (
	ErrJSTimeout        = errors.New("javascript execution timeout")
	ErrJSMemoryLimit    = errors.New("javascript memory limit exceeded")
	ErrJSSyntaxError    = errors.New("javascript syntax error")
	ErrJSRuntimeError   = errors.New("javascript runtime error")
	ErrJSFunctionNotFound = errors.New("javascript function not found")
)

// 消息处理相关错误
var (
	ErrInvalidMessage     = errors.New("invalid message format")
	ErrMessageProcessing  = errors.New("message processing failed")
	ErrNoHandlerFound     = errors.New("no handler found for message")
	ErrMaxRetriesExceeded = errors.New("max retries exceeded")
)

// 数据库相关错误
var (
	ErrDatabaseConnection = errors.New("database connection failed")
	ErrDatabaseQuery      = errors.New("database query failed")
	ErrDatabaseTransaction = errors.New("database transaction failed")
)

// MQ相关错误
var (
	ErrMQConnection    = errors.New("mq connection failed")
	ErrMQChannelClosed = errors.New("mq channel closed")
	ErrMQPublishFailed = errors.New("mq publish failed")
	ErrMQConsumeFailed = errors.New("mq consume failed")
)

// HTTP相关错误
var (
	ErrHTTPRequest  = errors.New("http request failed")
	ErrHTTPResponse = errors.New("http response error")
	ErrHTTPTimeout  = errors.New("http request timeout")
)

// 配置相关错误
var (
	ErrConfigNotFound = errors.New("config not found")
	ErrConfigInvalid  = errors.New("config invalid")
)

// ErrorCode 错误代码
type ErrorCode int

const (
	// 通用错误代码
	CodeSuccess ErrorCode = 0
	CodeInternalError ErrorCode = 1000
	CodeInvalidInput ErrorCode = 1001
	CodeNotFound ErrorCode = 1002
	CodeAlreadyExists ErrorCode = 1003
	CodeUnauthorized ErrorCode = 1004
	CodeForbidden ErrorCode = 1005

	// Handler相关错误代码
	CodeHandlerNotFound ErrorCode = 2001
	CodeHandlerAlreadyExists ErrorCode = 2002
	CodeHandlerDisabled ErrorCode = 2003
	CodeInvalidHandlerConfig ErrorCode = 2004

	// Proxy相关错误代码
	CodeProxyNotFound ErrorCode = 3001
	CodeProxyAlreadyExists ErrorCode = 3002
	CodeProxyDisabled ErrorCode = 3003
	CodeInvalidProxyConfig ErrorCode = 3004

	// JavaScript相关错误代码
	CodeJSTimeout ErrorCode = 4001
	CodeJSMemoryLimit ErrorCode = 4002
	CodeJSSyntaxError ErrorCode = 4003
	CodeJSRuntimeError ErrorCode = 4004

	// 消息处理相关错误代码
	CodeInvalidMessage ErrorCode = 5001
	CodeMessageProcessing ErrorCode = 5002
	CodeNoHandlerFound ErrorCode = 5003
	CodeMaxRetriesExceeded ErrorCode = 5004

	// 数据库相关错误代码
	CodeDatabaseError ErrorCode = 6001

	// MQ相关错误代码
	CodeMQError ErrorCode = 7001

	// HTTP相关错误代码
	CodeHTTPError ErrorCode = 8001
)

// APIError API错误响应
type APIError struct {
	Code    ErrorCode `json:"code"`
	Message string    `json:"message"`
	Details string    `json:"details,omitempty"`
}

// Error 实现error接口
func (e *APIError) Error() string {
	return e.Message
}

// NewAPIError 创建API错误
func NewAPIError(code ErrorCode, message string, details ...string) *APIError {
	err := &APIError{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// GetErrorCode 根据错误获取错误代码
func GetErrorCode(err error) ErrorCode {
	switch err {
	case ErrNotFound, ErrHandlerNotFound, ErrProxyNotFound:
		return CodeNotFound
	case ErrAlreadyExists, ErrHandlerAlreadyExists, ErrProxyAlreadyExists:
		return CodeAlreadyExists
	case ErrInvalidInput:
		return CodeInvalidInput
	case ErrUnauthorized:
		return CodeUnauthorized
	case ErrForbidden:
		return CodeForbidden
	case ErrHandlerDisabled:
		return CodeHandlerDisabled
	case ErrProxyDisabled:
		return CodeProxyDisabled
	case ErrJSTimeout:
		return CodeJSTimeout
	case ErrJSMemoryLimit:
		return CodeJSMemoryLimit
	case ErrJSSyntaxError:
		return CodeJSSyntaxError
	case ErrJSRuntimeError:
		return CodeJSRuntimeError
	case ErrInvalidMessage:
		return CodeInvalidMessage
	case ErrMessageProcessing:
		return CodeMessageProcessing
	case ErrNoHandlerFound:
		return CodeNoHandlerFound
	case ErrMaxRetriesExceeded:
		return CodeMaxRetriesExceeded
	default:
		return CodeInternalError
	}
}
