package handler

import (
	"fmt"
	"sync"
	"time"

	"messageweaver/internal/logger"
	"messageweaver/internal/models"
	"messageweaver/internal/proxy"
	"messageweaver/pkg/types"

	"go.uber.org/zap"
)

// Manager 处理器管理器
type Manager struct {
	logger       logger.Logger
	jsEngine     *JSEngine
	proxyManager *proxy.Manager
	handlers     map[string]*models.MessageHandler
	mutex        sync.RWMutex
}

// NewManager 创建处理器管理器
func NewManager(logger logger.Logger, proxyManager *proxy.Manager) *Manager {
	jsEngine := NewJSEngine(logger, 5*time.Second, 64*1024*1024) // 5秒超时，64MB内存限制

	return &Manager{
		logger:       logger,
		jsEngine:     jsEngine,
		proxyManager: proxyManager,
		handlers:     make(map[string]*models.MessageHandler),
	}
}

// LoadHandler 加载处理器
func (m *Manager) LoadHandler(handler *models.MessageHandler) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 验证处理器
	if err := handler.Validate(); err != nil {
		return fmt.Errorf("invalid handler: %w", err)
	}

	// 验证JavaScript代码
	if err := m.jsEngine.ValidateCode(handler.Code); err != nil {
		return fmt.Errorf("invalid JavaScript code: %w", err)
	}

	// 验证数据代理是否存在
	for _, proxyName := range handler.GetDataProxyNames() {
		if !m.proxyManager.HasProxy(proxyName) {
			return fmt.Errorf("data proxy not found: %s", proxyName)
		}
	}

	m.handlers[handler.Name] = handler
	m.logger.Info("Handler loaded", zap.String("name", handler.Name))

	return nil
}

// UnloadHandler 卸载处理器
func (m *Manager) UnloadHandler(name string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	delete(m.handlers, name)
	m.logger.Info("Handler unloaded", zap.String("name", name))
}

// GetHandler 获取处理器
func (m *Manager) GetHandler(name string) (*models.MessageHandler, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	handler, exists := m.handlers[name]
	return handler, exists
}

// GetHandlersForMQ 获取处理指定MQ队列的处理器
func (m *Manager) GetHandlersForMQ(mqName string) []*models.MessageHandler {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []*models.MessageHandler
	for _, handler := range m.handlers {
		if handler.Enabled && handler.IsValidForMQ(mqName) {
			result = append(result, handler)
		}
	}

	return result
}

// ProcessMessage 处理消息
func (m *Manager) ProcessMessage(handler *models.MessageHandler, message *types.StandardMessage) (*types.ProcessResult, error) {
	startTime := time.Now()

	// 执行JavaScript代码
	executeResult, err := m.jsEngine.Execute(handler, message)
	if err != nil {
		return &types.ProcessResult{
			Success:       false,
			Error:         err.Error(),
			ExecutionTime: time.Since(startTime).Milliseconds(),
		}, err
	}

	if !executeResult.Success {
		return &types.ProcessResult{
			Success:       false,
			Error:         executeResult.Error,
			ExecutionTime: executeResult.ExecutionTime.Milliseconds(),
			MemoryUsed:    fmt.Sprintf("%d bytes", executeResult.MemoryUsed),
		}, fmt.Errorf("JavaScript execution failed: %s", executeResult.Error)
	}

	// 如果结果为nil，表示跳过处理
	if executeResult.Result == nil {
		return &types.ProcessResult{
			Success:       true,
			ExecutionTime: executeResult.ExecutionTime.Milliseconds(),
			MemoryUsed:    fmt.Sprintf("%d bytes", executeResult.MemoryUsed),
		}, nil
	}

	// 转发到数据代理
	err = m.forwardToProxies(handler, executeResult.Result)
	if err != nil {
		return &types.ProcessResult{
			Success:       false,
			Error:         err.Error(),
			ExecutionTime: time.Since(startTime).Milliseconds(),
		}, err
	}

	return &types.ProcessResult{
		Success:       true,
		Data:          executeResult.Result.(map[string]interface{}),
		ExecutionTime: executeResult.ExecutionTime.Milliseconds(),
		MemoryUsed:    fmt.Sprintf("%d bytes", executeResult.MemoryUsed),
	}, nil
}

// forwardToProxies 转发到数据代理
func (m *Manager) forwardToProxies(handler *models.MessageHandler, data interface{}) error {
	for _, proxyName := range handler.GetDataProxyNames() {
		err := m.proxyManager.Send(proxyName, data)
		if err != nil {
			m.logger.Error("Failed to send to proxy",
				zap.String("handler", handler.Name),
				zap.String("proxy", proxyName),
				zap.Error(err),
			)
			return fmt.Errorf("failed to send to proxy %s: %w", proxyName, err)
		}
	}
	return nil
}

// TestHandler 测试处理器
func (m *Manager) TestHandler(handler *models.MessageHandler, testMessage *types.StandardMessage) (*ExecuteResult, error) {
	return m.jsEngine.Execute(handler, testMessage)
}

// ValidateCode 验证JavaScript代码
func (m *Manager) ValidateCode(code string) error {
	return m.jsEngine.ValidateCode(code)
}

// GetLoadedHandlers 获取已加载的处理器列表
func (m *Manager) GetLoadedHandlers() []*models.MessageHandler {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []*models.MessageHandler
	for _, handler := range m.handlers {
		result = append(result, handler)
	}

	return result
}

// GetHandlerCount 获取处理器数量
func (m *Manager) GetHandlerCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.handlers)
}

// GetEnabledHandlerCount 获取启用的处理器数量
func (m *Manager) GetEnabledHandlerCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	count := 0
	for _, handler := range m.handlers {
		if handler.Enabled {
			count++
		}
	}

	return count
}

// ReloadHandler 重新加载处理器
func (m *Manager) ReloadHandler(handler *models.MessageHandler) error {
	m.UnloadHandler(handler.Name)
	return m.LoadHandler(handler)
}

// GetMemoryUsage 获取内存使用情况
func (m *Manager) GetMemoryUsage() map[string]interface{} {
	return m.jsEngine.GetMemoryUsage()
}

// GetStatistics 获取统计信息
func (m *Manager) GetStatistics() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_handlers":   len(m.handlers),
		"enabled_handlers": m.GetEnabledHandlerCount(),
		"memory_usage":     m.GetMemoryUsage(),
	}

	// 按MQ队列统计
	mqStats := make(map[string]int)
	for _, handler := range m.handlers {
		if handler.Enabled {
			for _, mqName := range handler.MqNames {
				mqStats[mqName]++
			}
		}
	}
	stats["mq_handlers"] = mqStats

	return stats
}
