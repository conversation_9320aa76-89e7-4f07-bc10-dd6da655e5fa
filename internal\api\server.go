package api

import (
	"net/http"
	"time"

	"messageweaver/internal/config"
	"messageweaver/internal/database"
	"messageweaver/internal/handler"
	"messageweaver/internal/logger"
	"messageweaver/internal/proxy"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// Server API服务器
type Server struct {
	config         config.APIConfig
	logger         logger.Logger
	db             *database.Database
	handlerManager *handler.Manager
	proxyManager   *proxy.Manager
	router         *gin.Engine
}

// NewServer 创建API服务器
func NewServer(
	cfg config.APIConfig,
	logger logger.Logger,
	db *database.Database,
	handlerManager *handler.Manager,
	proxyManager *proxy.Manager,
) *Server {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)

	server := &Server{
		config:         cfg,
		logger:         logger,
		db:             db,
		handlerManager: handlerManager,
		proxyManager:   proxyManager,
		router:         gin.New(),
	}

	server.setupMiddleware()
	server.setupRoutes()

	return server
}

// setupMiddleware 设置中间件
func (s *Server) setupMiddleware() {
	// 日志中间件
	s.router.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		s.logger.Info("HTTP Request",
			zap.String("method", param.Method),
			zap.String("path", param.Path),
			zap.Int("status", param.StatusCode),
			zap.Duration("latency", param.Latency),
			zap.String("client_ip", param.ClientIP),
			zap.String("user_agent", param.Request.UserAgent()),
		)
		return ""
	}))

	// 恢复中间件
	s.router.Use(gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		s.logger.Error("Panic recovered",
			zap.Any("error", recovered),
			zap.String("path", c.Request.URL.Path),
		)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Internal server error",
		})
	}))

	// CORS中间件
	s.router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})

	// 超时中间件
	s.router.Use(func(c *gin.Context) {
		timeout := time.Duration(s.config.ReadTimeout) * time.Second
		c.Request = c.Request.WithContext(c.Request.Context())
		c.Next()
		_ = timeout // 避免未使用变量警告
	})
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 健康检查
	s.router.GET("/health", s.healthCheck)

	// API路由组
	api := s.router.Group("/api")
	{
		// MessageHandler路由
		handlers := api.Group("/message-handlers")
		{
			handlerController := NewHandlerController(s.db, s.handlerManager, s.logger)
			handlers.GET("", handlerController.List)
			handlers.POST("", handlerController.Create)
			handlers.GET("/:name", handlerController.GetByName)
			handlers.PUT("/:name", handlerController.Update)
			handlers.DELETE("/:name", handlerController.Delete)
			handlers.POST("/:name/test", handlerController.Test)
			handlers.POST("/:name/debug", handlerController.Debug)
			handlers.GET("/:name/syntax-check", handlerController.SyntaxCheck)
			handlers.PUT("/:name/status", handlerController.UpdateStatus)
		}

		// DataProxy路由
		proxies := api.Group("/data-proxies")
		{
			proxyController := NewProxyController(s.db, s.proxyManager, s.logger)
			proxies.GET("", proxyController.List)
			proxies.POST("", proxyController.Create)
			proxies.GET("/:name", proxyController.GetByName)
			proxies.PUT("/:name", proxyController.Update)
			proxies.DELETE("/:name", proxyController.Delete)
			proxies.POST("/:name/test", proxyController.TestConnection)
			proxies.PUT("/:name/status", proxyController.UpdateStatus)
		}

		// 日志路由
		logs := api.Group("/logs")
		{
			logController := NewLogController(s.db, s.logger)
			logs.GET("", logController.GetMessageLogs)
			logs.GET("/errors", logController.GetErrorLogs)
			logs.GET("/statistics", logController.GetStatistics)
		}

		// 监控路由
		monitoring := api.Group("/monitoring")
		{
			monitorController := NewMonitorController(s.db, s.handlerManager, s.proxyManager, s.logger)
			monitoring.GET("/metrics", monitorController.GetMetrics)
			monitoring.GET("/health", monitorController.GetHealthStatus)
			monitoring.GET("/queues/status", monitorController.GetQueueStatus)
			monitoring.GET("/data-proxies/status", monitorController.GetProxyStatus)
		}

		// 系统路由
		system := api.Group("/system")
		{
			systemController := NewSystemController(s.db, s.logger)
			system.GET("/info", systemController.GetSystemInfo)
			system.GET("/stats", systemController.GetSystemStats)
			system.POST("/cleanup", systemController.CleanupOldData)
		}
	}
}

// healthCheck 健康检查
func (s *Server) healthCheck(c *gin.Context) {
	status := map[string]interface{}{
		"status":    "ok",
		"timestamp": time.Now().UTC().Format(time.RFC3339),
		"version":   "1.0.0",
	}

	// 检查数据库连接
	if err := s.db.Health(); err != nil {
		status["database"] = "error"
		status["database_error"] = err.Error()
		c.JSON(http.StatusServiceUnavailable, status)
		return
	}
	status["database"] = "ok"

	// 检查处理器管理器
	status["handlers"] = map[string]interface{}{
		"total":   s.handlerManager.GetHandlerCount(),
		"enabled": s.handlerManager.GetEnabledHandlerCount(),
	}

	// 检查代理管理器
	status["proxies"] = map[string]interface{}{
		"total":   s.proxyManager.GetProxyCount(),
		"enabled": s.proxyManager.GetEnabledProxyCount(),
	}

	c.JSON(http.StatusOK, status)
}

// Router 获取路由器
func (s *Server) Router() *gin.Engine {
	return s.router
}

// Start 启动服务器
func (s *Server) Start() error {
	addr := ":" + string(rune(s.config.Port))
	s.logger.Info("Starting API server", zap.String("addr", addr))
	return s.router.Run(addr)
}

// Response 统一响应结构
type Response struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Code    int         `json:"code,omitempty"`
}

// SuccessResponse 成功响应
func SuccessResponse(data interface{}) Response {
	return Response{
		Success: true,
		Data:    data,
	}
}

// ErrorResponse 错误响应
func ErrorResponse(err error, code ...int) Response {
	resp := Response{
		Success: false,
		Error:   err.Error(),
	}
	if len(code) > 0 {
		resp.Code = code[0]
	}
	return resp
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Items      interface{} `json:"items"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	Limit      int         `json:"limit"`
	TotalPages int         `json:"totalPages"`
}

// NewPaginationResponse 创建分页响应
func NewPaginationResponse(items interface{}, total int64, page, limit int) PaginationResponse {
	totalPages := int(total) / limit
	if int(total)%limit > 0 {
		totalPages++
	}

	return PaginationResponse{
		Items:      items,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}
}
