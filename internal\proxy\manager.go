package proxy

import (
	"fmt"
	"sync"

	"messageweaver/internal/logger"
	"messageweaver/internal/models"

	"go.uber.org/zap"
)

// Manager 代理管理器
type Manager struct {
	logger   logger.Logger
	proxies  map[string]DataProxy
	factory  ProxyFactory
	mutex    sync.RWMutex
}

// NewManager 创建代理管理器
func NewManager(logger logger.Logger) *Manager {
	return &Manager{
		logger:  logger,
		proxies: make(map[string]DataProxy),
		factory: NewDefaultFactory(logger),
	}
}

// LoadProxy 加载代理
func (m *Manager) LoadProxy(config *models.DataProxy) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 验证配置
	if err := config.BeforeCreate(nil); err != nil {
		return fmt.Errorf("invalid proxy config: %w", err)
	}

	// 创建代理
	proxy, err := m.factory.CreateProxy(config)
	if err != nil {
		return fmt.Errorf("failed to create proxy: %w", err)
	}

	// 测试连接
	if err := proxy.TestConnection(); err != nil {
		proxy.Close()
		return fmt.Errorf("proxy connection test failed: %w", err)
	}

	// 关闭现有代理（如果存在）
	if existingProxy, exists := m.proxies[config.Name]; exists {
		existingProxy.Close()
	}

	m.proxies[config.Name] = proxy
	m.logger.Info("Proxy loaded", zap.String("name", config.Name), zap.String("type", string(config.Type)))

	return nil
}

// UnloadProxy 卸载代理
func (m *Manager) UnloadProxy(name string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	proxy, exists := m.proxies[name]
	if !exists {
		return fmt.Errorf("proxy not found: %s", name)
	}

	if err := proxy.Close(); err != nil {
		m.logger.Error("Failed to close proxy", zap.String("name", name), zap.Error(err))
	}

	delete(m.proxies, name)
	m.logger.Info("Proxy unloaded", zap.String("name", name))

	return nil
}

// GetProxy 获取代理
func (m *Manager) GetProxy(name string) (DataProxy, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	proxy, exists := m.proxies[name]
	return proxy, exists
}

// HasProxy 检查代理是否存在
func (m *Manager) HasProxy(name string) bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	_, exists := m.proxies[name]
	return exists
}

// Send 发送数据到指定代理
func (m *Manager) Send(proxyName string, data interface{}) error {
	proxy, exists := m.GetProxy(proxyName)
	if !exists {
		return fmt.Errorf("proxy not found: %s", proxyName)
	}

	if !proxy.IsEnabled() {
		return fmt.Errorf("proxy is disabled: %s", proxyName)
	}

	return proxy.Send(data)
}

// SendToMultiple 发送数据到多个代理
func (m *Manager) SendToMultiple(proxyNames []string, data interface{}) error {
	var errors []error

	for _, proxyName := range proxyNames {
		if err := m.Send(proxyName, data); err != nil {
			errors = append(errors, fmt.Errorf("proxy %s: %w", proxyName, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("multiple proxy errors: %v", errors)
	}

	return nil
}

// GetLoadedProxies 获取已加载的代理列表
func (m *Manager) GetLoadedProxies() []DataProxy {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var result []DataProxy
	for _, proxy := range m.proxies {
		result = append(result, proxy)
	}

	return result
}

// GetProxyCount 获取代理数量
func (m *Manager) GetProxyCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return len(m.proxies)
}

// GetEnabledProxyCount 获取启用的代理数量
func (m *Manager) GetEnabledProxyCount() int {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	count := 0
	for _, proxy := range m.proxies {
		if proxy.IsEnabled() {
			count++
		}
	}

	return count
}

// TestAllConnections 测试所有代理连接
func (m *Manager) TestAllConnections() map[string]error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	results := make(map[string]error)
	for name, proxy := range m.proxies {
		results[name] = proxy.TestConnection()
	}

	return results
}

// GetStatistics 获取统计信息
func (m *Manager) GetStatistics() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_proxies":   len(m.proxies),
		"enabled_proxies": m.GetEnabledProxyCount(),
	}

	// 按类型统计
	typeStats := make(map[string]int)
	for _, proxy := range m.proxies {
		typeKey := string(proxy.GetType())
		typeStats[typeKey]++
	}
	stats["by_type"] = typeStats

	// 代理状态
	proxyStatuses := make(map[string]interface{})
	for name, proxy := range m.proxies {
		proxyStatuses[name] = proxy.GetStatus()
	}
	stats["proxy_status"] = proxyStatuses

	return stats
}

// ReloadProxy 重新加载代理
func (m *Manager) ReloadProxy(config *models.DataProxy) error {
	if err := m.UnloadProxy(config.Name); err != nil {
		m.logger.Warn("Failed to unload existing proxy", zap.String("name", config.Name), zap.Error(err))
	}
	return m.LoadProxy(config)
}

// Close 关闭所有代理
func (m *Manager) Close() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	var errors []error
	for name, proxy := range m.proxies {
		if err := proxy.Close(); err != nil {
			errors = append(errors, fmt.Errorf("proxy %s: %w", name, err))
		}
	}

	m.proxies = make(map[string]DataProxy)

	if len(errors) > 0 {
		return fmt.Errorf("multiple close errors: %v", errors)
	}

	return nil
}

// HealthCheck 健康检查
func (m *Manager) HealthCheck() map[string]bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	results := make(map[string]bool)
	for name, proxy := range m.proxies {
		err := proxy.TestConnection()
		results[name] = err == nil
	}

	return results
}
