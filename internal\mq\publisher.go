package mq

import (
	"encoding/json"
	"fmt"
	"time"

	"messageweaver/internal/config"
	"messageweaver/internal/logger"
	"messageweaver/pkg/types"

	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

// Publisher 消息发布者
type Publisher struct {
	config     config.RabbitMQConfig
	logger     logger.Logger
	connection *Connection
}

// NewPublisher 创建消息发布者
func NewPublisher(cfg config.RabbitMQConfig, logger logger.Logger) (*Publisher, error) {
	conn, err := NewConnection(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}

	publisher := &Publisher{
		config:     cfg,
		logger:     logger,
		connection: conn,
	}

	// 设置交换机
	if err := publisher.setupExchanges(); err != nil {
		return nil, fmt.Errorf("failed to setup exchanges: %w", err)
	}

	return publisher, nil
}

// setupExchanges 设置交换机
func (p *Publisher) setupExchanges() error {
	// 声明主交换机
	if err := p.connection.DeclareExchange(
		p.config.Exchange,
		p.config.ExchangeType,
		true,  // durable
		false, // auto-delete
	); err != nil {
		return fmt.Errorf("failed to declare exchange %s: %w", p.config.Exchange, err)
	}

	// 声明死信交换机
	if err := p.connection.DeclareExchange(
		p.config.DeadLetterExchange,
		"direct",
		true,  // durable
		false, // auto-delete
	); err != nil {
		return fmt.Errorf("failed to declare dead letter exchange %s: %w", p.config.DeadLetterExchange, err)
	}

	return nil
}

// PublishMessage 发布标准消息
func (p *Publisher) PublishMessage(message *types.StandardMessage, routingKey string) error {
	// 序列化消息
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// 创建发布消息
	publishing := amqp.Publishing{
		Headers: amqp.Table{
			"source":        message.Source,
			"eventType":     message.EventType,
			"priority":      message.Metadata.Priority,
			"correlationId": message.Metadata.CorrelationId,
		},
		MessageId:    message.MessageId,
		Timestamp:    time.Now(),
		ContentType:  "application/json",
		DeliveryMode: amqp.Persistent,
		Body:         body,
	}

	// 发布消息
	if err := p.connection.Publish(p.config.Exchange, routingKey, publishing); err != nil {
		return fmt.Errorf("failed to publish message: %w", err)
	}

	p.logger.Debug("Message published",
		zap.String("message_id", message.MessageId),
		zap.String("routing_key", routingKey),
		zap.String("event_type", message.EventType),
	)

	return nil
}

// PublishRawMessage 发布原始消息
func (p *Publisher) PublishRawMessage(body []byte, routingKey string, headers map[string]interface{}) error {
	// 创建发布消息
	publishing := amqp.Publishing{
		Headers:      amqp.Table(headers),
		Timestamp:    time.Now(),
		ContentType:  "application/json",
		DeliveryMode: amqp.Persistent,
		Body:         body,
	}

	// 发布消息
	if err := p.connection.Publish(p.config.Exchange, routingKey, publishing); err != nil {
		return fmt.Errorf("failed to publish raw message: %w", err)
	}

	p.logger.Debug("Raw message published",
		zap.String("routing_key", routingKey),
		zap.Int("body_size", len(body)),
	)

	return nil
}

// PublishToDeadLetter 发布到死信队列
func (p *Publisher) PublishToDeadLetter(message *types.DeadLetterMessage) error {
	// 序列化死信消息
	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal dead letter message: %w", err)
	}

	// 创建发布消息
	publishing := amqp.Publishing{
		Headers: amqp.Table{
			"x-original-message-id": message.OriginalMessage.MessageId,
			"x-failure-reason":      message.Reason,
			"x-retry-count":         message.RetryCount,
		},
		Timestamp:    time.Now(),
		ContentType:  "application/json",
		DeliveryMode: amqp.Persistent,
		Body:         body,
	}

	// 发布到死信交换机
	if err := p.connection.Publish(p.config.DeadLetterExchange, "failed", publishing); err != nil {
		return fmt.Errorf("failed to publish to dead letter: %w", err)
	}

	p.logger.Info("Message sent to dead letter queue",
		zap.String("original_message_id", message.OriginalMessage.MessageId),
		zap.String("reason", message.Reason),
		zap.Int("retry_count", message.RetryCount),
	)

	return nil
}

// PublishBatch 批量发布消息
func (p *Publisher) PublishBatch(messages []*types.StandardMessage, routingKey string) error {
	for _, message := range messages {
		if err := p.PublishMessage(message, routingKey); err != nil {
			return fmt.Errorf("failed to publish message %s: %w", message.MessageId, err)
		}
	}

	p.logger.Info("Batch messages published",
		zap.Int("count", len(messages)),
		zap.String("routing_key", routingKey),
	)

	return nil
}

// PublishWithConfirm 发布消息并等待确认
func (p *Publisher) PublishWithConfirm(message *types.StandardMessage, routingKey string) error {
	channel, err := p.connection.GetChannel()
	if err != nil {
		return err
	}

	// 启用发布确认
	if err := channel.Confirm(false); err != nil {
		return fmt.Errorf("failed to enable confirm mode: %w", err)
	}

	// 监听确认
	confirms := channel.NotifyPublish(make(chan amqp.Confirmation, 1))

	// 发布消息
	if err := p.PublishMessage(message, routingKey); err != nil {
		return err
	}

	// 等待确认
	select {
	case confirm := <-confirms:
		if !confirm.Ack {
			return fmt.Errorf("message not acknowledged")
		}
	case <-time.After(5 * time.Second):
		return fmt.Errorf("publish confirmation timeout")
	}

	return nil
}

// CreateTestMessage 创建测试消息
func (p *Publisher) CreateTestMessage(eventType string, data map[string]interface{}) *types.StandardMessage {
	return &types.StandardMessage{
		MessageId: fmt.Sprintf("test-%d", time.Now().UnixNano()),
		Timestamp: time.Now().UTC().Format(time.RFC3339),
		Source:    "test-publisher",
		EventType: eventType,
		Version:   "1.0",
		Data:      data,
		Metadata: types.MessageMetadata{
			Priority:      "normal",
			RetryCount:    0,
			MaxRetries:    3,
			Tags:          []string{"test"},
			CorrelationId: fmt.Sprintf("test-corr-%d", time.Now().UnixNano()),
		},
	}
}

// GetStatus 获取发布者状态
func (p *Publisher) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"connected":  p.connection.IsConnected(),
		"connection": p.connection.GetConnectionInfo(),
		"exchange":   p.config.Exchange,
	}
}

// Close 关闭发布者
func (p *Publisher) Close() error {
	return p.connection.Close()
}
