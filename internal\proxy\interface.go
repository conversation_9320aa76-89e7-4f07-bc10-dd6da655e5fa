package proxy

import (
	"messageweaver/internal/models"
)

// DataProxy 数据代理接口
type DataProxy interface {
	// Send 发送数据
	Send(data interface{}) error
	
	// GetName 获取代理名称
	GetName() string
	
	// GetType 获取代理类型
	GetType() models.DataProxyType
	
	// IsEnabled 是否启用
	IsEnabled() bool
	
	// TestConnection 测试连接
	TestConnection() error
	
	// Close 关闭连接
	Close() error
	
	// GetStatus 获取状态
	GetStatus() map[string]interface{}
}

// ProxyFactory 代理工厂接口
type ProxyFactory interface {
	// CreateProxy 创建代理
	CreateProxy(config *models.DataProxy) (DataProxy, error)
	
	// GetSupportedTypes 获取支持的类型
	GetSupportedTypes() []models.DataProxyType
}

// ConnectionPool 连接池接口
type ConnectionPool interface {
	// Get 获取连接
	Get() (interface{}, error)
	
	// Put 归还连接
	Put(conn interface{}) error
	
	// Close 关闭连接池
	Close() error
	
	// Stats 获取统计信息
	Stats() map[string]interface{}
}

// RetryPolicy 重试策略接口
type RetryPolicy interface {
	// ShouldRetry 是否应该重试
	ShouldRetry(attempt int, err error) bool
	
	// GetDelay 获取延迟时间
	GetDelay(attempt int) int64
	
	// GetMaxAttempts 获取最大重试次数
	GetMaxAttempts() int
}

// CircuitBreaker 断路器接口
type CircuitBreaker interface {
	// Execute 执行操作
	Execute(operation func() error) error
	
	// GetState 获取状态
	GetState() string
	
	// Reset 重置
	Reset()
}

// HealthChecker 健康检查接口
type HealthChecker interface {
	// Check 检查健康状态
	Check() error
	
	// GetLastCheck 获取最后检查时间
	GetLastCheck() int64
	
	// IsHealthy 是否健康
	IsHealthy() bool
}
