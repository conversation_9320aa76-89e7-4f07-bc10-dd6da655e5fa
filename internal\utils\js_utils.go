package utils

import (
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
	"bytes"
)

// JSUtils JavaScript工具库实现
type JSUtils struct {
	httpClient *http.Client
}

// NewJSUtils 创建JavaScript工具库
func NewJSUtils() *JSUtils {
	return &JSUtils{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Now 获取当前ISO时间
func (u *JSUtils) Now() string {
	return time.Now().UTC().Format(time.RFC3339)
}

// Timestamp 获取Unix时间戳
func (u *JSUtils) Timestamp() int64 {
	return time.Now().Unix()
}

// ParseTime 解析时间字符串
func (u *JSUtils) ParseTime(str string) (time.Time, error) {
	// 尝试多种时间格式
	formats := []string{
		time.RFC3339,
		time.RFC3339Nano,
		"2006-01-02T15:04:05Z",
		"2006-01-02 15:04:05",
		"2006-01-02",
	}
	
	for _, format := range formats {
		if t, err := time.Parse(format, str); err == nil {
			return t, nil
		}
	}
	
	return time.Time{}, fmt.Errorf("unable to parse time: %s", str)
}

// DeepClone 深度克隆对象
func (u *JSUtils) DeepClone(obj interface{}) interface{} {
	// 通过JSON序列化/反序列化实现深拷贝
	data, err := json.Marshal(obj)
	if err != nil {
		return nil
	}
	
	var result interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		return nil
	}
	
	return result
}

// Merge 合并两个对象
func (u *JSUtils) Merge(obj1, obj2 map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	
	// 复制第一个对象
	for k, v := range obj1 {
		result[k] = v
	}
	
	// 合并第二个对象
	for k, v := range obj2 {
		result[k] = v
	}
	
	return result
}

// Pick 选择指定字段
func (u *JSUtils) Pick(obj map[string]interface{}, keys []string) map[string]interface{} {
	result := make(map[string]interface{})
	
	for _, key := range keys {
		if value, exists := obj[key]; exists {
			result[key] = value
		}
	}
	
	return result
}

// IsEmail 验证邮箱格式
func (u *JSUtils) IsEmail(str string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(str)
}

// IsPhone 验证手机号格式（中国手机号）
func (u *JSUtils) IsPhone(str string) bool {
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(str)
}

// IsUrl 验证URL格式
func (u *JSUtils) IsUrl(str string) bool {
	_, err := url.ParseRequestURI(str)
	return err == nil
}

// MD5 计算MD5哈希
func (u *JSUtils) MD5(str string) string {
	hash := md5.Sum([]byte(str))
	return fmt.Sprintf("%x", hash)
}

// SHA256 计算SHA256哈希
func (u *JSUtils) SHA256(str string) string {
	hash := sha256.Sum256([]byte(str))
	return fmt.Sprintf("%x", hash)
}

// Base64Encode Base64编码
func (u *JSUtils) Base64Encode(str string) string {
	return base64.StdEncoding.EncodeToString([]byte(str))
}

// Base64Decode Base64解码
func (u *JSUtils) Base64Decode(str string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// HTTPGet 发送GET请求
func (u *JSUtils) HTTPGet(url string, headers map[string]string) (map[string]interface{}, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}
	
	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	
	resp, err := u.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}
	
	return result, nil
}

// HTTPPost 发送POST请求
func (u *JSUtils) HTTPPost(url string, data interface{}, headers map[string]string) (map[string]interface{}, error) {
	// 序列化请求数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	
	// 设置默认Content-Type
	req.Header.Set("Content-Type", "application/json")
	
	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}
	
	resp, err := u.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	var result map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, err
	}
	
	return result, nil
}

// StringUtils 字符串工具函数
type StringUtils struct{}

// NewStringUtils 创建字符串工具
func NewStringUtils() *StringUtils {
	return &StringUtils{}
}

// Trim 去除首尾空格
func (s *StringUtils) Trim(str string) string {
	return strings.TrimSpace(str)
}

// ToUpper 转换为大写
func (s *StringUtils) ToUpper(str string) string {
	return strings.ToUpper(str)
}

// ToLower 转换为小写
func (s *StringUtils) ToLower(str string) string {
	return strings.ToLower(str)
}

// Contains 检查是否包含子字符串
func (s *StringUtils) Contains(str, substr string) bool {
	return strings.Contains(str, substr)
}

// Replace 替换字符串
func (s *StringUtils) Replace(str, old, new string) string {
	return strings.ReplaceAll(str, old, new)
}

// Split 分割字符串
func (s *StringUtils) Split(str, sep string) []string {
	return strings.Split(str, sep)
}

// Join 连接字符串
func (s *StringUtils) Join(strs []string, sep string) string {
	return strings.Join(strs, sep)
}

// ArrayUtils 数组工具函数
type ArrayUtils struct{}

// NewArrayUtils 创建数组工具
func NewArrayUtils() *ArrayUtils {
	return &ArrayUtils{}
}

// Contains 检查数组是否包含元素
func (a *ArrayUtils) Contains(arr []interface{}, item interface{}) bool {
	for _, v := range arr {
		if v == item {
			return true
		}
	}
	return false
}

// Unique 数组去重
func (a *ArrayUtils) Unique(arr []interface{}) []interface{} {
	seen := make(map[interface{}]bool)
	var result []interface{}
	
	for _, item := range arr {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// Filter 过滤数组
func (a *ArrayUtils) Filter(arr []interface{}, predicate func(interface{}) bool) []interface{} {
	var result []interface{}
	
	for _, item := range arr {
		if predicate(item) {
			result = append(result, item)
		}
	}
	
	return result
}

// Map 映射数组
func (a *ArrayUtils) Map(arr []interface{}, mapper func(interface{}) interface{}) []interface{} {
	result := make([]interface{}, len(arr))
	
	for i, item := range arr {
		result[i] = mapper(item)
	}
	
	return result
}
