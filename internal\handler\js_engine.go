package handler

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"messageweaver/internal/logger"
	"messageweaver/internal/models"
	"messageweaver/internal/utils"
	"messageweaver/pkg/types"

	"github.com/dop251/goja"
)

// JSEngine JavaScript执行引擎
type JSEngine struct {
	logger      logger.Logger
	jsUtils     *utils.JSUtils
	timeout     time.Duration
	memoryLimit int64
}

// NewJSEngine 创建JavaScript引擎
func NewJSEngine(logger logger.Logger, timeout time.Duration, memoryLimit int64) *JSEngine {
	return &JSEngine{
		logger:      logger,
		jsUtils:     utils.NewJSUtils(),
		timeout:     timeout,
		memoryLimit: memoryLimit,
	}
}

// ExecuteResult 执行结果
type ExecuteResult struct {
	Success       bool          `json:"success"`
	Result        interface{}   `json:"result,omitempty"`
	Error         string        `json:"error,omitempty"`
	ExecutionTime time.Duration `json:"executionTime"`
	MemoryUsed    int64         `json:"memoryUsed"`
}

// Execute 执行JavaScript代码
func (e *JSEngine) Execute(handler *models.MessageHandler, message *types.StandardMessage) (*ExecuteResult, error) {
	startTime := time.Now()
	var memBefore runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&memBefore)

	// 创建执行上下文
	ctx, cancel := context.WithTimeout(context.Background(), e.timeout)
	defer cancel()

	// 创建goja运行时
	vm := goja.New()

	// 设置中断处理
	vm.SetInterruptHandler(func() bool {
		select {
		case <-ctx.Done():
			return true
		default:
			return false
		}
	})

	// 创建JavaScript上下文
	jsContext := e.createJSContext(handler, message)

	// 注入全局对象
	if err := e.injectGlobals(vm, jsContext); err != nil {
		return &ExecuteResult{
			Success:       false,
			Error:         fmt.Sprintf("Failed to inject globals: %v", err),
			ExecutionTime: time.Since(startTime),
		}, err
	}

	// 执行代码
	result, err := e.executeCode(vm, handler.Code, message, jsContext)

	// 计算内存使用
	var memAfter runtime.MemStats
	runtime.GC()
	runtime.ReadMemStats(&memAfter)
	memoryUsed := int64(memAfter.Alloc - memBefore.Alloc)

	executionTime := time.Since(startTime)

	if err != nil {
		return &ExecuteResult{
			Success:       false,
			Error:         err.Error(),
			ExecutionTime: executionTime,
			MemoryUsed:    memoryUsed,
		}, err
	}

	return &ExecuteResult{
		Success:       true,
		Result:        result,
		ExecutionTime: executionTime,
		MemoryUsed:    memoryUsed,
	}, nil
}

// createJSContext 创建JavaScript执行上下文
func (e *JSEngine) createJSContext(handler *models.MessageHandler, message *types.StandardMessage) *types.JSContext {
	// 创建日志器
	jsLogger := logger.NewJSLogger(e.logger)
	jsLogger.SetFields(map[string]interface{}{
		"handler":    handler.Name,
		"message_id": message.MessageId,
	})

	return &types.JSContext{
		Logger:       jsLogger,
		Utils:        e.jsUtils,
		Config:       handler.Config,
		GlobalConfig: make(map[string]interface{}), // 可以从配置中加载
	}
}

// injectGlobals 注入全局对象
func (e *JSEngine) injectGlobals(vm *goja.Runtime, jsContext *types.JSContext) error {
	// 注入console对象
	console := vm.NewObject()
	console.Set("log", func(args ...interface{}) {
		data := make(map[string]interface{})
		for i, arg := range args {
			data[fmt.Sprintf("arg%d", i)] = arg
		}
		jsContext.Logger.Info("console.log", data)
	})
	console.Set("error", func(args ...interface{}) {
		data := make(map[string]interface{})
		for i, arg := range args {
			data[fmt.Sprintf("arg%d", i)] = arg
		}
		jsContext.Logger.Error("console.error", data)
	})
	vm.Set("console", console)

	// 注入context对象
	contextObj := vm.NewObject()

	// 注入logger
	loggerObj := vm.NewObject()
	loggerObj.Set("debug", jsContext.Logger.Debug)
	loggerObj.Set("info", jsContext.Logger.Info)
	loggerObj.Set("warn", jsContext.Logger.Warn)
	loggerObj.Set("error", jsContext.Logger.Error)
	contextObj.Set("logger", loggerObj)

	// 注入utils
	utilsObj := vm.NewObject()
	utilsObj.Set("now", jsContext.Utils.Now)
	utilsObj.Set("timestamp", jsContext.Utils.Timestamp)
	utilsObj.Set("parseTime", jsContext.Utils.ParseTime)
	utilsObj.Set("deepClone", jsContext.Utils.DeepClone)
	utilsObj.Set("merge", jsContext.Utils.Merge)
	utilsObj.Set("pick", jsContext.Utils.Pick)
	utilsObj.Set("isEmail", jsContext.Utils.IsEmail)
	utilsObj.Set("isPhone", jsContext.Utils.IsPhone)
	utilsObj.Set("isUrl", jsContext.Utils.IsUrl)
	utilsObj.Set("md5", jsContext.Utils.MD5)
	utilsObj.Set("sha256", jsContext.Utils.SHA256)
	utilsObj.Set("base64Encode", jsContext.Utils.Base64Encode)
	utilsObj.Set("base64Decode", jsContext.Utils.Base64Decode)
	utilsObj.Set("httpGet", jsContext.Utils.HTTPGet)
	utilsObj.Set("httpPost", jsContext.Utils.HTTPPost)
	contextObj.Set("utils", utilsObj)

	// 注入配置
	contextObj.Set("config", jsContext.Config)
	contextObj.Set("globalConfig", jsContext.GlobalConfig)

	vm.Set("context", contextObj)

	return nil
}

// executeCode 执行JavaScript代码
func (e *JSEngine) executeCode(vm *goja.Runtime, code string, message *types.StandardMessage, jsContext *types.JSContext) (interface{}, error) {
	// 编译并执行代码
	_, err := vm.RunString(code)
	if err != nil {
		return nil, fmt.Errorf("failed to compile code: %w", err)
	}

	// 获取process函数
	processFunc, ok := goja.AssertFunction(vm.Get("process"))
	if !ok {
		return nil, fmt.Errorf("process function not found")
	}

	// 调用process函数
	result, err := processFunc(goja.Undefined(), vm.ToValue(message), vm.ToValue(jsContext))
	if err != nil {
		return nil, fmt.Errorf("failed to execute process function: %w", err)
	}

	// 转换结果
	if result == nil || goja.IsUndefined(result) || goja.IsNull(result) {
		return nil, nil
	}

	return result.Export(), nil
}

// ValidateCode 验证JavaScript代码
func (e *JSEngine) ValidateCode(code string) error {
	vm := goja.New()

	// 尝试编译代码
	_, err := vm.RunString(code)
	if err != nil {
		return fmt.Errorf("syntax error: %w", err)
	}

	// 检查是否存在process函数
	processFunc := vm.Get("process")
	if processFunc == nil || goja.IsUndefined(processFunc) {
		return fmt.Errorf("process function not found")
	}

	if !goja.IsFunction(processFunc) {
		return fmt.Errorf("process is not a function")
	}

	return nil
}

// TestCode 测试JavaScript代码
func (e *JSEngine) TestCode(code string, testMessage *types.StandardMessage) (*ExecuteResult, error) {
	// 创建临时处理器
	handler := &models.MessageHandler{
		Name:    "test-handler",
		Code:    code,
		Config:  make(map[string]interface{}),
		Enabled: true,
	}

	return e.Execute(handler, testMessage)
}

// GetMemoryUsage 获取内存使用情况
func (e *JSEngine) GetMemoryUsage() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"alloc":       m.Alloc,
		"total_alloc": m.TotalAlloc,
		"sys":         m.Sys,
		"num_gc":      m.NumGC,
		"heap_alloc":  m.HeapAlloc,
		"heap_sys":    m.HeapSys,
		"heap_idle":   m.HeapIdle,
		"heap_inuse":  m.HeapInuse,
		"stack_inuse": m.StackInuse,
		"stack_sys":   m.StackSys,
	}
}
