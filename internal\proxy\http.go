package proxy

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"messageweaver/internal/logger"
	"messageweaver/internal/models"

	"go.uber.org/zap"
)

// HTTPProxy HTTP数据代理
type HTTPProxy struct {
	config     *models.DataProxy
	logger     logger.Logger
	httpClient *http.Client
	enabled    bool
}

// NewHTTPProxy 创建HTTP代理
func NewHTTPProxy(config *models.DataProxy, logger logger.Logger) (*HTTPProxy, error) {
	if config.Type != models.ProxyTypeHTTP {
		return nil, fmt.Errorf("invalid proxy type: %s", config.Type)
	}

	// 创建HTTP客户端
	timeout := time.Duration(config.GetTimeout()) * time.Millisecond
	httpClient := &http.Client{
		Timeout: timeout,
	}

	proxy := &HTTPProxy{
		config:     config,
		logger:     logger,
		httpClient: httpClient,
		enabled:    config.Enabled,
	}

	return proxy, nil
}

// Send 发送数据
func (p *HTTPProxy) Send(data interface{}) error {
	if !p.enabled {
		return fmt.Errorf("proxy is disabled")
	}

	url := p.config.GetURL()
	if url == "" {
		return fmt.Errorf("missing URL in config")
	}

	method := p.config.GetMethod()
	headers := p.config.GetHeaders()

	// 序列化数据
	var body io.Reader
	if data != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			return fmt.Errorf("failed to marshal data: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	// 创建请求
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	for key, value := range headers {
		req.Header.Set(key, p.expandVariables(value))
	}

	// 发送请求
	startTime := time.Now()
	resp, err := p.httpClient.Do(req)
	duration := time.Since(startTime)

	if err != nil {
		p.logger.Error("HTTP request failed",
			zap.String("proxy", p.config.Name),
			zap.String("url", url),
			zap.String("method", method),
			zap.Duration("duration", duration),
			zap.Error(err),
		)
		return fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		p.logger.Error("Failed to read response body",
			zap.String("proxy", p.config.Name),
			zap.Int("status_code", resp.StatusCode),
			zap.Error(err),
		)
		return fmt.Errorf("failed to read response: %w", err)
	}

	// 检查响应状态
	if resp.StatusCode >= 400 {
		p.logger.Error("HTTP request returned error status",
			zap.String("proxy", p.config.Name),
			zap.String("url", url),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", string(respBody)),
		)
		return fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(respBody))
	}

	// 记录成功日志
	p.logger.Debug("HTTP request successful",
		zap.String("proxy", p.config.Name),
		zap.String("url", url),
		zap.String("method", method),
		zap.Int("status_code", resp.StatusCode),
		zap.Duration("duration", duration),
		zap.Int("response_size", len(respBody)),
	)

	return nil
}

// expandVariables 展开变量（如环境变量）
func (p *HTTPProxy) expandVariables(value string) string {
	// 简单的变量替换，可以扩展支持更多变量
	if strings.Contains(value, "${API_TOKEN}") {
		// 这里可以从环境变量或配置中获取实际值
		// 为了安全，实际实现中应该从安全的地方获取
		return strings.ReplaceAll(value, "${API_TOKEN}", "actual-token-value")
	}
	return value
}

// GetName 获取代理名称
func (p *HTTPProxy) GetName() string {
	return p.config.Name
}

// GetType 获取代理类型
func (p *HTTPProxy) GetType() models.DataProxyType {
	return p.config.Type
}

// IsEnabled 是否启用
func (p *HTTPProxy) IsEnabled() bool {
	return p.enabled
}

// TestConnection 测试连接
func (p *HTTPProxy) TestConnection() error {
	url := p.config.GetURL()
	if url == "" {
		return fmt.Errorf("missing URL in config")
	}

	// 发送HEAD请求测试连接
	req, err := http.NewRequest("HEAD", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create test request: %w", err)
	}

	// 设置较短的超时时间用于测试
	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("connection test failed: %w", err)
	}
	defer resp.Body.Close()

	// 2xx和4xx状态码都认为连接正常（4xx表示服务器可达但可能不支持HEAD方法）
	if resp.StatusCode >= 500 {
		return fmt.Errorf("server error: status %d", resp.StatusCode)
	}

	return nil
}

// Close 关闭连接
func (p *HTTPProxy) Close() error {
	// HTTP客户端不需要显式关闭
	return nil
}

// GetStatus 获取状态
func (p *HTTPProxy) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"name":    p.config.Name,
		"type":    p.config.Type,
		"enabled": p.enabled,
		"url":     p.config.GetURL(),
		"method":  p.config.GetMethod(),
		"timeout": p.config.GetTimeout(),
	}

	// 测试连接
	if err := p.TestConnection(); err != nil {
		status["connection_error"] = err.Error()
		status["healthy"] = false
	} else {
		status["healthy"] = true
	}

	return status
}

// SetEnabled 设置启用状态
func (p *HTTPProxy) SetEnabled(enabled bool) {
	p.enabled = enabled
}

// SendWithRetry 带重试的发送
func (p *HTTPProxy) SendWithRetry(data interface{}, maxRetries int) error {
	var lastErr error
	
	for attempt := 0; attempt <= maxRetries; attempt++ {
		err := p.Send(data)
		if err == nil {
			return nil
		}
		
		lastErr = err
		
		// 如果是客户端错误（4xx），不重试
		if strings.Contains(err.Error(), "status 4") {
			break
		}
		
		if attempt < maxRetries {
			// 指数退避
			delay := time.Duration(1<<uint(attempt)) * time.Second
			p.logger.Warn("HTTP request failed, retrying",
				zap.String("proxy", p.config.Name),
				zap.Int("attempt", attempt+1),
				zap.Int("max_retries", maxRetries),
				zap.Duration("delay", delay),
				zap.Error(err),
			)
			time.Sleep(delay)
		}
	}
	
	return fmt.Errorf("max retries exceeded: %w", lastErr)
}

// SendBatch 批量发送数据
func (p *HTTPProxy) SendBatch(dataList []interface{}) error {
	if !p.enabled {
		return fmt.Errorf("proxy is disabled")
	}

	// 将数据包装为批量格式
	batchData := map[string]interface{}{
		"batch": dataList,
		"count": len(dataList),
		"timestamp": time.Now().UTC().Format(time.RFC3339),
	}

	return p.Send(batchData)
}

// GetMetrics 获取指标
func (p *HTTPProxy) GetMetrics() map[string]interface{} {
	return map[string]interface{}{
		"proxy_name": p.config.Name,
		"proxy_type": p.config.Type,
		"enabled":    p.enabled,
		"url":        p.config.GetURL(),
		"timeout":    p.config.GetTimeout(),
	}
}
