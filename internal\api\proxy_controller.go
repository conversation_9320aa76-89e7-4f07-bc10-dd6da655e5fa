package api

import (
	"net/http"
	"strconv"

	"messageweaver/internal/database"
	"messageweaver/internal/logger"
	"messageweaver/internal/models"
	"messageweaver/internal/proxy"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ProxyController 代理控制器
type ProxyController struct {
	db           *database.Database
	proxyDAO     *database.ProxyDAO
	proxyManager *proxy.Manager
	logger       logger.Logger
}

// NewProxyController 创建代理控制器
func NewProxyController(db *database.Database, proxyManager *proxy.Manager, logger logger.Logger) *ProxyController {
	return &ProxyController{
		db:           db,
		proxyDAO:     database.NewProxyDAO(db),
		proxyManager: proxyManager,
		logger:       logger,
	}
}

// List 获取代理列表
func (pc *ProxyController) List(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	typeStr := c.Query("type")
	enabledStr := c.Query("enabled")

	var proxyType *models.DataProxyType
	if typeStr != "" {
		t := models.DataProxyType(typeStr)
		proxyType = &t
	}

	var enabled *bool
	if enabledStr != "" {
		e := enabledStr == "true"
		enabled = &e
	}

	offset := (page - 1) * limit

	// 查询数据
	proxies, total, err := pc.proxyDAO.List(offset, limit, proxyType, enabled)
	if err != nil {
		pc.logger.Error("Failed to list proxies", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 转换为响应格式
	var items []map[string]interface{}
	for _, proxy := range proxies {
		items = append(items, proxy.ToMap())
	}

	response := NewPaginationResponse(items, total, page, limit)
	c.JSON(http.StatusOK, SuccessResponse(response))
}

// Create 创建代理
func (pc *ProxyController) Create(c *gin.Context) {
	var req models.DataProxy
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 创建代理
	if err := pc.proxyDAO.Create(&req); err != nil {
		pc.logger.Error("Failed to create proxy", zap.String("name", req.Name), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 加载到管理器
	if err := pc.proxyManager.LoadProxy(&req); err != nil {
		pc.logger.Error("Failed to load proxy", zap.String("name", req.Name), zap.Error(err))
		// 即使加载失败，也不删除数据库记录，只记录错误
	}

	pc.logger.Info("Proxy created", zap.String("name", req.Name), zap.String("type", string(req.Type)))
	c.JSON(http.StatusCreated, SuccessResponse(req.ToMap()))
}

// GetByName 根据名称获取代理
func (pc *ProxyController) GetByName(c *gin.Context) {
	name := c.Param("name")

	proxy, err := pc.proxyDAO.GetByName(name)
	if err != nil {
		if err == models.ErrProxyNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			pc.logger.Error("Failed to get proxy", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(proxy.ToMap()))
}

// Update 更新代理
func (pc *ProxyController) Update(c *gin.Context) {
	name := c.Param("name")

	var req models.DataProxy
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 获取现有代理
	existing, err := pc.proxyDAO.GetByName(name)
	if err != nil {
		if err == models.ErrProxyNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			pc.logger.Error("Failed to get proxy", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	// 更新字段
	req.ID = existing.ID
	req.Name = name // 确保名称不变

	// 更新数据库
	if err := pc.proxyDAO.Update(&req); err != nil {
		pc.logger.Error("Failed to update proxy", zap.String("name", name), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 重新加载到管理器
	if err := pc.proxyManager.ReloadProxy(&req); err != nil {
		pc.logger.Error("Failed to reload proxy", zap.String("name", name), zap.Error(err))
	}

	pc.logger.Info("Proxy updated", zap.String("name", name))
	c.JSON(http.StatusOK, SuccessResponse(req.ToMap()))
}

// Delete 删除代理
func (pc *ProxyController) Delete(c *gin.Context) {
	name := c.Param("name")

	// 从管理器卸载
	if err := pc.proxyManager.UnloadProxy(name); err != nil {
		pc.logger.Warn("Failed to unload proxy", zap.String("name", name), zap.Error(err))
	}

	// 从数据库删除
	if err := pc.proxyDAO.DeleteByName(name); err != nil {
		if err == models.ErrProxyNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			pc.logger.Error("Failed to delete proxy", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	pc.logger.Info("Proxy deleted", zap.String("name", name))
	c.JSON(http.StatusOK, SuccessResponse(map[string]string{"message": "Proxy deleted successfully"}))
}

// TestConnection 测试代理连接
func (pc *ProxyController) TestConnection(c *gin.Context) {
	name := c.Param("name")

	// 从管理器获取代理
	proxyInstance, exists := pc.proxyManager.GetProxy(name)
	if !exists {
		c.JSON(http.StatusNotFound, ErrorResponse(models.ErrProxyNotFound))
		return
	}

	// 测试连接
	err := proxyInstance.TestConnection()
	if err != nil {
		pc.logger.Error("Proxy connection test failed", zap.String("name", name), zap.Error(err))
		c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}))
		return
	}

	pc.logger.Info("Proxy connection test successful", zap.String("name", name))
	c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
		"success": true,
		"message": "Connection test successful",
	}))
}

// UpdateStatus 更新代理状态
func (pc *ProxyController) UpdateStatus(c *gin.Context) {
	name := c.Param("name")

	var req struct {
		Enabled bool `json:"enabled"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 更新数据库状态
	if err := pc.proxyDAO.UpdateStatusByName(name, req.Enabled); err != nil {
		if err == models.ErrProxyNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			pc.logger.Error("Failed to update proxy status", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	// 重新加载代理
	if req.Enabled {
		proxy, err := pc.proxyDAO.GetByName(name)
		if err == nil {
			pc.proxyManager.LoadProxy(proxy)
		}
	} else {
		pc.proxyManager.UnloadProxy(name)
	}

	pc.logger.Info("Proxy status updated", zap.String("name", name), zap.Bool("enabled", req.Enabled))
	c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
		"name":    name,
		"enabled": req.Enabled,
	}))
}

// GetTypes 获取支持的代理类型
func (pc *ProxyController) GetTypes(c *gin.Context) {
	types := []map[string]interface{}{
		{
			"type":        models.ProxyTypeMySQL,
			"name":        "MySQL Database",
			"description": "MySQL数据库代理",
		},
		{
			"type":        models.ProxyTypeDM,
			"name":        "DM Database",
			"description": "达梦数据库代理",
		},
		{
			"type":        models.ProxyTypeHTTP,
			"name":        "HTTP API",
			"description": "HTTP接口代理",
		},
	}

	c.JSON(http.StatusOK, SuccessResponse(types))
}

// GetTemplate 获取代理配置模板
func (pc *ProxyController) GetTemplate(c *gin.Context) {
	proxyType := c.Query("type")
	if proxyType == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse(models.ErrInvalidInput))
		return
	}

	template := proxy.GetDefaultConfig(models.DataProxyType(proxyType), "example-proxy")
	c.JSON(http.StatusOK, SuccessResponse(template))
}

// SendTestData 发送测试数据
func (pc *ProxyController) SendTestData(c *gin.Context) {
	name := c.Param("name")

	var req struct {
		Data interface{} `json:"data"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 发送测试数据
	if err := pc.proxyManager.Send(name, req.Data); err != nil {
		pc.logger.Error("Failed to send test data", zap.String("name", name), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	pc.logger.Info("Test data sent successfully", zap.String("name", name))
	c.JSON(http.StatusOK, SuccessResponse(map[string]string{
		"message": "Test data sent successfully",
	}))
}
