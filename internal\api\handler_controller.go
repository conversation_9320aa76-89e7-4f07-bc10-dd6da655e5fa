package api

import (
	"net/http"
	"strconv"

	"messageweaver/internal/database"
	"messageweaver/internal/handler"
	"messageweaver/internal/logger"
	"messageweaver/internal/models"
	"messageweaver/pkg/types"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// HandlerController 处理器控制器
type HandlerController struct {
	db             *database.Database
	handlerDAO     *database.HandlerDAO
	handlerManager *handler.Manager
	logger         logger.Logger
}

// NewHandlerController 创建处理器控制器
func NewHandlerController(db *database.Database, handlerManager *handler.Manager, logger logger.Logger) *HandlerController {
	return &HandlerController{
		db:             db,
		handlerDAO:     database.NewHandlerDAO(db),
		handlerManager: handlerManager,
		logger:         logger,
	}
}

// List 获取处理器列表
func (hc *HandlerController) List(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>("limit", "10"))
	enabledStr := c.Query("enabled")

	var enabled *bool
	if enabledStr != "" {
		e := enabledStr == "true"
		enabled = &e
	}

	offset := (page - 1) * limit

	// 查询数据
	handlers, total, err := hc.handlerDAO.List(offset, limit, enabled)
	if err != nil {
		hc.logger.Error("Failed to list handlers", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 转换为响应格式
	var items []map[string]interface{}
	for _, handler := range handlers {
		items = append(items, handler.ToMap())
	}

	response := NewPaginationResponse(items, total, page, limit)
	c.JSON(http.StatusOK, SuccessResponse(response))
}

// Create 创建处理器
func (hc *HandlerController) Create(c *gin.Context) {
	var req models.MessageHandler
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 创建处理器
	if err := hc.handlerDAO.Create(&req); err != nil {
		hc.logger.Error("Failed to create handler", zap.String("name", req.Name), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 加载到管理器
	if err := hc.handlerManager.LoadHandler(&req); err != nil {
		hc.logger.Error("Failed to load handler", zap.String("name", req.Name), zap.Error(err))
		// 即使加载失败，也不删除数据库记录，只记录错误
	}

	hc.logger.Info("Handler created", zap.String("name", req.Name))
	c.JSON(http.StatusCreated, SuccessResponse(req.ToMap()))
}

// GetByName 根据名称获取处理器
func (hc *HandlerController) GetByName(c *gin.Context) {
	name := c.Param("name")

	handler, err := hc.handlerDAO.GetByName(name)
	if err != nil {
		if err == models.ErrHandlerNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			hc.logger.Error("Failed to get handler", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(handler.ToMap()))
}

// Update 更新处理器
func (hc *HandlerController) Update(c *gin.Context) {
	name := c.Param("name")

	var req models.MessageHandler
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 获取现有处理器
	existing, err := hc.handlerDAO.GetByName(name)
	if err != nil {
		if err == models.ErrHandlerNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			hc.logger.Error("Failed to get handler", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	// 更新字段
	req.ID = existing.ID
	req.Name = name // 确保名称不变

	// 更新数据库
	if err := hc.handlerDAO.Update(&req); err != nil {
		hc.logger.Error("Failed to update handler", zap.String("name", name), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 重新加载到管理器
	if err := hc.handlerManager.ReloadHandler(&req); err != nil {
		hc.logger.Error("Failed to reload handler", zap.String("name", name), zap.Error(err))
	}

	hc.logger.Info("Handler updated", zap.String("name", name))
	c.JSON(http.StatusOK, SuccessResponse(req.ToMap()))
}

// Delete 删除处理器
func (hc *HandlerController) Delete(c *gin.Context) {
	name := c.Param("name")

	// 从管理器卸载
	hc.handlerManager.UnloadHandler(name)

	// 从数据库删除
	if err := hc.handlerDAO.DeleteByName(name); err != nil {
		if err == models.ErrHandlerNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			hc.logger.Error("Failed to delete handler", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	hc.logger.Info("Handler deleted", zap.String("name", name))
	c.JSON(http.StatusOK, SuccessResponse(map[string]string{"message": "Handler deleted successfully"}))
}

// Test 测试处理器
func (hc *HandlerController) Test(c *gin.Context) {
	name := c.Param("name")

	var req struct {
		Message types.StandardMessage `json:"message"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 获取处理器
	handler, err := hc.handlerDAO.GetByName(name)
	if err != nil {
		if err == models.ErrHandlerNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			hc.logger.Error("Failed to get handler", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	// 测试执行
	result, err := hc.handlerManager.TestHandler(handler, &req.Message)
	if err != nil {
		hc.logger.Error("Handler test failed", zap.String("name", name), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
		"result":         result.Result,
		"success":        result.Success,
		"error":          result.Error,
		"execution_time": result.ExecutionTime.Milliseconds(),
		"memory_used":    result.MemoryUsed,
	}))
}

// Debug 调试处理器
func (hc *HandlerController) Debug(c *gin.Context) {
	name := c.Param("name")

	var req struct {
		Message types.StandardMessage `json:"message"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 获取处理器
	handler, err := hc.handlerDAO.GetByName(name)
	if err != nil {
		if err == models.ErrHandlerNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			hc.logger.Error("Failed to get handler", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	// 调试执行（包含详细日志）
	result, err := hc.handlerManager.TestHandler(handler, &req.Message)
	if err != nil {
		hc.logger.Error("Handler debug failed", zap.String("name", name), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 返回详细的调试信息
	debugInfo := map[string]interface{}{
		"handler_info": map[string]interface{}{
			"name":        handler.Name,
			"version":     handler.Version,
			"enabled":     handler.Enabled,
			"mq_names":    handler.MqNames,
			"proxy_names": handler.DataProxyNames,
		},
		"execution_result": map[string]interface{}{
			"result":         result.Result,
			"success":        result.Success,
			"error":          result.Error,
			"execution_time": result.ExecutionTime.Milliseconds(),
			"memory_used":    result.MemoryUsed,
		},
		"input_message": req.Message,
	}

	c.JSON(http.StatusOK, SuccessResponse(debugInfo))
}

// SyntaxCheck 语法检查
func (hc *HandlerController) SyntaxCheck(c *gin.Context) {
	name := c.Param("name")

	// 获取处理器
	handler, err := hc.handlerDAO.GetByName(name)
	if err != nil {
		if err == models.ErrHandlerNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			hc.logger.Error("Failed to get handler", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	// 语法检查
	if err := hc.handlerManager.ValidateCode(handler.Code); err != nil {
		c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
			"valid": false,
			"error": err.Error(),
		}))
		return
	}

	c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
		"valid": true,
	}))
}

// UpdateStatus 更新处理器状态
func (hc *HandlerController) UpdateStatus(c *gin.Context) {
	name := c.Param("name")

	var req struct {
		Enabled bool `json:"enabled"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse(err))
		return
	}

	// 更新数据库状态
	if err := hc.handlerDAO.UpdateStatusByName(name, req.Enabled); err != nil {
		if err == models.ErrHandlerNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse(err))
		} else {
			hc.logger.Error("Failed to update handler status", zap.String("name", name), zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		}
		return
	}

	// 重新加载处理器
	if req.Enabled {
		handler, err := hc.handlerDAO.GetByName(name)
		if err == nil {
			hc.handlerManager.LoadHandler(handler)
		}
	} else {
		hc.handlerManager.UnloadHandler(name)
	}

	hc.logger.Info("Handler status updated", zap.String("name", name), zap.Bool("enabled", req.Enabled))
	c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
		"name":    name,
		"enabled": req.Enabled,
	}))
}
