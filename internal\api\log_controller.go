package api

import (
	"net/http"
	"strconv"
	"time"

	"messageweaver/internal/database"
	"messageweaver/internal/logger"
	"messageweaver/internal/models"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// LogController 日志控制器
type LogController struct {
	db     *database.Database
	logDAO *database.LogDAO
	logger logger.Logger
}

// NewLogController 创建日志控制器
func NewLogController(db *database.Database, logger logger.Logger) *LogController {
	return &LogController{
		db:     db,
		logDAO: database.NewLogDAO(db),
		logger: logger,
	}
}

// GetMessageLogs 获取消息处理日志
func (lc *LogController) GetMessageLogs(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON>uer<PERSON>("limit", "10"))

	filters := make(map[string]interface{})

	if handlerName := c.Query("handler_name"); handlerName != "" {
		filters["handler_name"] = handlerName
	}

	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}

	if eventType := c.Query("event_type"); eventType != "" {
		filters["event_type"] = eventType
	}

	if source := c.Query("source"); source != "" {
		filters["source"] = source
	}

	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			filters["start_time"] = t
		}
	}

	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			filters["end_time"] = t
		}
	}

	offset := (page - 1) * limit

	// 查询数据
	logs, total, err := lc.logDAO.ListMessageLogs(offset, limit, filters)
	if err != nil {
		lc.logger.Error("Failed to list message logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 转换为响应格式
	var items []map[string]interface{}
	for _, log := range logs {
		items = append(items, log.ToMap())
	}

	response := NewPaginationResponse(items, total, page, limit)
	c.JSON(http.StatusOK, SuccessResponse(response))
}

// GetErrorLogs 获取错误日志
func (lc *LogController) GetErrorLogs(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// 默认查询最近24小时的错误日志
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour)

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = t
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = t
		}
	}

	offset := (page - 1) * limit

	// 查询错误日志
	logs, total, err := lc.logDAO.GetErrorLogs(offset, limit, startTime, endTime)
	if err != nil {
		lc.logger.Error("Failed to get error logs", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 转换为响应格式
	var items []map[string]interface{}
	for _, log := range logs {
		items = append(items, log.ToMap())
	}

	response := NewPaginationResponse(items, total, page, limit)
	c.JSON(http.StatusOK, SuccessResponse(response))
}

// GetStatistics 获取统计信息
func (lc *LogController) GetStatistics(c *gin.Context) {
	// 解析时间范围参数
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour) // 默认最近24小时

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = t
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = t
		}
	}

	// 获取消息处理统计
	messageStats, err := lc.logDAO.GetMessageLogStatistics(startTime, endTime)
	if err != nil {
		lc.logger.Error("Failed to get message statistics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 获取处理器统计
	handlerStats, err := lc.logDAO.GetHandlerStatistics(startTime, endTime)
	if err != nil {
		lc.logger.Error("Failed to get handler statistics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	statistics := map[string]interface{}{
		"time_range": map[string]interface{}{
			"start_time": startTime.Format(time.RFC3339),
			"end_time":   endTime.Format(time.RFC3339),
		},
		"message_statistics": messageStats,
		"handler_statistics": handlerStats,
	}

	c.JSON(http.StatusOK, SuccessResponse(statistics))
}

// GetSystemMetrics 获取系统指标
func (lc *LogController) GetSystemMetrics(c *gin.Context) {
	metricName := c.Query("metric_name")
	limitStr := c.DefaultQuery("limit", "100")
	limit, _ := strconv.Atoi(limitStr)

	// 解析时间范围
	endTime := time.Now()
	startTime := endTime.Add(-1 * time.Hour) // 默认最近1小时

	if startTimeStr := c.Query("start_time"); startTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			startTime = t
		}
	}

	if endTimeStr := c.Query("end_time"); endTimeStr != "" {
		if t, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			endTime = t
		}
	}

	if metricName == "" {
		// 获取最新的所有指标
		metrics, err := lc.logDAO.GetLatestSystemMetrics()
		if err != nil {
			lc.logger.Error("Failed to get latest system metrics", zap.Error(err))
			c.JSON(http.StatusInternalServerError, ErrorResponse(err))
			return
		}

		c.JSON(http.StatusOK, SuccessResponse(metrics))
		return
	}

	// 获取指定指标的历史数据
	metrics, err := lc.logDAO.GetSystemMetrics(metricName, startTime, endTime, limit)
	if err != nil {
		lc.logger.Error("Failed to get system metrics",
			zap.String("metric_name", metricName),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	response := map[string]interface{}{
		"metric_name": metricName,
		"time_range": map[string]interface{}{
			"start_time": startTime.Format(time.RFC3339),
			"end_time":   endTime.Format(time.RFC3339),
		},
		"data": metrics,
	}

	c.JSON(http.StatusOK, SuccessResponse(response))
}

// GetLogsByMessageID 根据消息ID获取日志
func (lc *LogController) GetLogsByMessageID(c *gin.Context) {
	messageID := c.Param("messageId")
	if messageID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse(models.ErrInvalidInput))
		return
	}

	logs, err := lc.logDAO.GetMessageLogByMessageID(messageID)
	if err != nil {
		lc.logger.Error("Failed to get logs by message ID",
			zap.String("message_id", messageID),
			zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	// 转换为响应格式
	var items []map[string]interface{}
	for _, log := range logs {
		items = append(items, log.ToMap())
	}

	c.JSON(http.StatusOK, SuccessResponse(items))
}

// CleanupOldLogs 清理旧日志
func (lc *LogController) CleanupOldLogs(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse(models.ErrInvalidInput))
		return
	}

	if err := lc.logDAO.CleanupOldLogs(days); err != nil {
		lc.logger.Error("Failed to cleanup old logs", zap.Int("days", days), zap.Error(err))
		c.JSON(http.StatusInternalServerError, ErrorResponse(err))
		return
	}

	lc.logger.Info("Old logs cleaned up", zap.Int("days", days))
	c.JSON(http.StatusOK, SuccessResponse(map[string]interface{}{
		"message": "Old logs cleaned up successfully",
		"days":    days,
	}))
}
