package types

import (
	"encoding/json"
	"time"
)

// StandardMessage 标准消息结构
type StandardMessage struct {
	MessageId string                 `json:"messageId" validate:"required"`
	Timestamp string                 `json:"timestamp" validate:"required"`
	Source    string                 `json:"source" validate:"required"`
	EventType string                 `json:"eventType" validate:"required"`
	Version   string                 `json:"version" validate:"required"`
	Data      map[string]interface{} `json:"data" validate:"required"`
	Metadata  MessageMetadata        `json:"metadata"`
}

// MessageMetadata 消息元数据
type MessageMetadata struct {
	Priority      string   `json:"priority"`
	RetryCount    int      `json:"retryCount"`
	MaxRetries    int      `json:"maxRetries"`
	Tags          []string `json:"tags"`
	CorrelationId string   `json:"correlationId"`
}

// ProcessResult 处理结果
type ProcessResult struct {
	Success      bool                   `json:"success"`
	Data         map[string]interface{} `json:"data,omitempty"`
	Error        string                 `json:"error,omitempty"`
	ExecutionTime int64                 `json:"executionTime"` // 毫秒
	MemoryUsed   string                 `json:"memoryUsed"`
}

// DeadLetterMessage 死信消息
type DeadLetterMessage struct {
	OriginalMessage *StandardMessage `json:"originalMessage"`
	Error          string           `json:"error"`
	RetryCount     int              `json:"retryCount"`
	FailedAt       time.Time        `json:"failedAt"`
	Reason         string           `json:"reason"`
}

// JSContext JavaScript执行上下文
type JSContext struct {
	Logger       JSLogger                   `json:"-"`
	Utils        JSUtils                    `json:"-"`
	Config       map[string]interface{}     `json:"config"`
	GlobalConfig map[string]interface{}     `json:"globalConfig"`
}

// JSLogger JavaScript日志接口
type JSLogger interface {
	Debug(message string, data map[string]interface{})
	Info(message string, data map[string]interface{})
	Warn(message string, data map[string]interface{})
	Error(message string, data map[string]interface{})
}

// JSUtils JavaScript工具接口
type JSUtils interface {
	Now() string
	Timestamp() int64
	ParseTime(str string) (time.Time, error)
	DeepClone(obj interface{}) interface{}
	Merge(obj1, obj2 map[string]interface{}) map[string]interface{}
	Pick(obj map[string]interface{}, keys []string) map[string]interface{}
	IsEmail(str string) bool
	IsPhone(str string) bool
	IsUrl(str string) bool
	MD5(str string) string
	SHA256(str string) string
	Base64Encode(str string) string
	Base64Decode(str string) (string, error)
	HTTPGet(url string, headers map[string]string) (map[string]interface{}, error)
	HTTPPost(url string, data interface{}, headers map[string]string) (map[string]interface{}, error)
}

// ToJSON 转换为JSON字符串
func (m *StandardMessage) ToJSON() (string, error) {
	data, err := json.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (m *StandardMessage) FromJSON(data string) error {
	return json.Unmarshal([]byte(data), m)
}

// GetPriority 获取消息优先级
func (m *StandardMessage) GetPriority() string {
	if m.Metadata.Priority == "" {
		return "normal"
	}
	return m.Metadata.Priority
}

// ShouldRetry 判断是否应该重试
func (m *StandardMessage) ShouldRetry() bool {
	return m.Metadata.RetryCount < m.Metadata.MaxRetries
}

// IncrementRetry 增加重试次数
func (m *StandardMessage) IncrementRetry() {
	m.Metadata.RetryCount++
}

// HasTag 检查是否包含指定标签
func (m *StandardMessage) HasTag(tag string) bool {
	for _, t := range m.Metadata.Tags {
		if t == tag {
			return true
		}
	}
	return false
}
