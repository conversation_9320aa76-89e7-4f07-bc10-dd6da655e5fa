package database

import (
	"time"

	"messageweaver/internal/models"
)

// LogDAO 日志数据访问对象
type LogDAO struct {
	db *Database
}

// NewLogDAO 创建LogDAO
func NewLogDAO(db *Database) *LogDAO {
	return &LogDAO{db: db}
}

// CreateMessageLog 创建消息日志
func (dao *LogDAO) CreateMessageLog(log *models.MessageLog) error {
	return dao.db.Create(log).Error
}

// UpdateMessageLog 更新消息日志
func (dao *LogDAO) UpdateMessageLog(log *models.MessageLog) error {
	return dao.db.Save(log).Error
}

// GetMessageLogByID 根据ID获取消息日志
func (dao *LogDAO) GetMessageLogByID(id uint) (*models.MessageLog, error) {
	var log models.MessageLog
	err := dao.db.First(&log, id).Error
	if err != nil {
		if dao.db.IsRecordNotFound(err) {
			return nil, models.ErrNotFound
		}
		return nil, err
	}
	return &log, nil
}

// GetMessageLogByMessageID 根据消息ID获取日志
func (dao *LogDAO) GetMessageLogByMessageID(messageId string) ([]*models.MessageLog, error) {
	var logs []*models.MessageLog
	err := dao.db.Where("message_id = ?", messageId).Order("created_at DESC").Find(&logs).Error
	return logs, err
}

// ListMessageLogs 获取消息日志列表
func (dao *LogDAO) ListMessageLogs(offset, limit int, filters map[string]interface{}) ([]*models.MessageLog, int64, error) {
	var logs []*models.MessageLog
	var total int64

	query := dao.db.Model(&models.MessageLog{})

	// 添加过滤条件
	if handlerName, ok := filters["handler_name"]; ok {
		query = query.Where("handler_name = ?", handlerName)
	}
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if eventType, ok := filters["event_type"]; ok {
		query = query.Where("event_type = ?", eventType)
	}
	if source, ok := filters["source"]; ok {
		query = query.Where("source = ?", source)
	}
	if startTime, ok := filters["start_time"]; ok {
		query = query.Where("processed_at >= ?", startTime)
	}
	if endTime, ok := filters["end_time"]; ok {
		query = query.Where("processed_at <= ?", endTime)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("processed_at DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetMessageLogStatistics 获取消息日志统计
func (dao *LogDAO) GetMessageLogStatistics(startTime, endTime time.Time) (map[string]interface{}, error) {
	var stats struct {
		Total      int64 `gorm:"column:total"`
		Success    int64 `gorm:"column:success"`
		Failed     int64 `gorm:"column:failed"`
		Processing int64 `gorm:"column:processing"`
		Retry      int64 `gorm:"column:retry"`
		AvgTime    float64 `gorm:"column:avg_time"`
	}

	err := dao.db.Model(&models.MessageLog{}).
		Where("processed_at BETWEEN ? AND ?", startTime, endTime).
		Select(`
			COUNT(*) as total,
			SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success,
			SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
			SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
			SUM(CASE WHEN status = 'retry' THEN 1 ELSE 0 END) as retry,
			AVG(CASE WHEN execution_time > 0 THEN execution_time ELSE NULL END) as avg_time
		`).Scan(&stats).Error

	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total":       stats.Total,
		"success":     stats.Success,
		"failed":      stats.Failed,
		"processing":  stats.Processing,
		"retry":       stats.Retry,
		"avg_time":    stats.AvgTime,
		"success_rate": func() float64 {
			if stats.Total > 0 {
				return float64(stats.Success) / float64(stats.Total) * 100
			}
			return 0
		}(),
	}, nil
}

// GetHandlerStatistics 获取处理器统计
func (dao *LogDAO) GetHandlerStatistics(startTime, endTime time.Time) ([]map[string]interface{}, error) {
	var stats []struct {
		HandlerName string  `gorm:"column:handler_name"`
		Total       int64   `gorm:"column:total"`
		Success     int64   `gorm:"column:success"`
		Failed      int64   `gorm:"column:failed"`
		AvgTime     float64 `gorm:"column:avg_time"`
	}

	err := dao.db.Model(&models.MessageLog{}).
		Where("processed_at BETWEEN ? AND ?", startTime, endTime).
		Select(`
			handler_name,
			COUNT(*) as total,
			SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success,
			SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
			AVG(CASE WHEN execution_time > 0 THEN execution_time ELSE NULL END) as avg_time
		`).Group("handler_name").Scan(&stats).Error

	if err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, len(stats))
	for i, stat := range stats {
		result[i] = map[string]interface{}{
			"handler_name": stat.HandlerName,
			"total":        stat.Total,
			"success":      stat.Success,
			"failed":       stat.Failed,
			"avg_time":     stat.AvgTime,
			"success_rate": func() float64 {
				if stat.Total > 0 {
					return float64(stat.Success) / float64(stat.Total) * 100
				}
				return 0
			}(),
		}
	}

	return result, nil
}

// CreateSystemMetric 创建系统指标
func (dao *LogDAO) CreateSystemMetric(metric *models.SystemMetric) error {
	return dao.db.Create(metric).Error
}

// BatchCreateSystemMetrics 批量创建系统指标
func (dao *LogDAO) BatchCreateSystemMetrics(metrics []*models.SystemMetric) error {
	return dao.db.CreateInBatches(metrics, 100).Error
}

// GetSystemMetrics 获取系统指标
func (dao *LogDAO) GetSystemMetrics(metricName string, startTime, endTime time.Time, limit int) ([]*models.SystemMetric, error) {
	var metrics []*models.SystemMetric
	
	query := dao.db.Where("metric_name = ? AND timestamp BETWEEN ? AND ?", metricName, startTime, endTime)
	if limit > 0 {
		query = query.Limit(limit)
	}
	
	err := query.Order("timestamp DESC").Find(&metrics).Error
	return metrics, err
}

// GetLatestSystemMetrics 获取最新的系统指标
func (dao *LogDAO) GetLatestSystemMetrics() (map[string]*models.SystemMetric, error) {
	var metrics []*models.SystemMetric
	
	// 获取每个指标的最新值
	err := dao.db.Raw(`
		SELECT m1.* FROM system_metrics m1
		INNER JOIN (
			SELECT metric_name, MAX(timestamp) as max_timestamp
			FROM system_metrics
			GROUP BY metric_name
		) m2 ON m1.metric_name = m2.metric_name AND m1.timestamp = m2.max_timestamp
	`).Scan(&metrics).Error

	if err != nil {
		return nil, err
	}

	result := make(map[string]*models.SystemMetric)
	for _, metric := range metrics {
		result[metric.MetricName] = metric
	}

	return result, nil
}

// CleanupOldLogs 清理旧日志
func (dao *LogDAO) CleanupOldLogs(days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)
	
	// 删除旧的消息日志
	if err := dao.db.Where("created_at < ?", cutoff).Delete(&models.MessageLog{}).Error; err != nil {
		return err
	}

	// 删除旧的系统指标
	if err := dao.db.Where("timestamp < ?", cutoff).Delete(&models.SystemMetric{}).Error; err != nil {
		return err
	}

	return nil
}

// GetErrorLogs 获取错误日志
func (dao *LogDAO) GetErrorLogs(offset, limit int, startTime, endTime time.Time) ([]*models.MessageLog, int64, error) {
	var logs []*models.MessageLog
	var total int64

	query := dao.db.Model(&models.MessageLog{}).
		Where("status = ? AND processed_at BETWEEN ? AND ?", models.StatusFailed, startTime, endTime)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("processed_at DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}
