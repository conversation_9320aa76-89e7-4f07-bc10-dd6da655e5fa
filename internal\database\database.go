package database

import (
	"fmt"
	"time"

	"messageweaver/internal/config"
	"messageweaver/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Database 数据库实例
type Database struct {
	*gorm.DB
}

// New 创建数据库连接
func New(cfg config.DatabaseConfig) (*Database, error) {
	// 配置GORM日志
	gormLogger := logger.Default
	if cfg.Driver == "mysql" {
		gormLogger = logger.Default.LogMode(logger.Info)
	}

	// 连接数据库
	var dialector gorm.Dialector
	switch cfg.Driver {
	case "mysql":
		dialector = mysql.Open(cfg.DSN)
	default:
		return nil, fmt.Errorf("unsupported database driver: %s", cfg.Driver)
	}

	db, err := gorm.Open(dialector, &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB实例
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get sql.DB instance: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.ConnMaxLifetime) * time.Second)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	database := &Database{DB: db}

	// 自动迁移表结构
	if err := database.AutoMigrate(); err != nil {
		return nil, fmt.Errorf("failed to auto migrate: %w", err)
	}

	return database, nil
}

// AutoMigrate 自动迁移表结构
func (db *Database) AutoMigrate() error {
	return db.DB.AutoMigrate(
		&models.MessageHandler{},
		&models.DataProxy{},
		&models.MessageLog{},
		&models.SystemMetric{},
	)
}

// Close 关闭数据库连接
func (db *Database) Close() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// Health 健康检查
func (db *Database) Health() error {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Ping()
}

// GetStats 获取连接池统计信息
func (db *Database) GetStats() map[string]interface{} {
	sqlDB, err := db.DB.DB()
	if err != nil {
		return map[string]interface{}{
			"error": err.Error(),
		}
	}

	stats := sqlDB.Stats()
	return map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration.String(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}
}

// Transaction 执行事务
func (db *Database) Transaction(fn func(*gorm.DB) error) error {
	return db.DB.Transaction(fn)
}

// IsRecordNotFound 判断是否为记录不存在错误
func (db *Database) IsRecordNotFound(err error) bool {
	return err == gorm.ErrRecordNotFound
}

// CreateIndexes 创建索引
func (db *Database) CreateIndexes() error {
	// 为message_logs表创建复合索引
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_message_logs_handler_status 
		ON message_logs(handler_name, status)
	`).Error; err != nil {
		return err
	}

	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_message_logs_event_type_processed 
		ON message_logs(event_type, processed_at)
	`).Error; err != nil {
		return err
	}

	// 为system_metrics表创建复合索引
	if err := db.Exec(`
		CREATE INDEX IF NOT EXISTS idx_system_metrics_name_timestamp 
		ON system_metrics(metric_name, timestamp)
	`).Error; err != nil {
		return err
	}

	return nil
}

// CleanupOldLogs 清理旧日志
func (db *Database) CleanupOldLogs(days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)
	
	// 删除旧的消息日志
	if err := db.Where("created_at < ?", cutoff).Delete(&models.MessageLog{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup old message logs: %w", err)
	}

	// 删除旧的系统指标
	if err := db.Where("timestamp < ?", cutoff).Delete(&models.SystemMetric{}).Error; err != nil {
		return fmt.Errorf("failed to cleanup old system metrics: %w", err)
	}

	return nil
}

// GetTableSizes 获取表大小信息
func (db *Database) GetTableSizes() (map[string]interface{}, error) {
	var results []struct {
		TableName string `gorm:"column:table_name"`
		DataLength int64 `gorm:"column:data_length"`
		IndexLength int64 `gorm:"column:index_length"`
		TableRows int64 `gorm:"column:table_rows"`
	}

	err := db.Raw(`
		SELECT 
			table_name,
			data_length,
			index_length,
			table_rows
		FROM information_schema.tables 
		WHERE table_schema = DATABASE()
		AND table_name IN ('message_handlers', 'data_proxies', 'message_logs', 'system_metrics')
	`).Scan(&results).Error

	if err != nil {
		return nil, err
	}

	sizes := make(map[string]interface{})
	for _, result := range results {
		sizes[result.TableName] = map[string]interface{}{
			"data_length":  result.DataLength,
			"index_length": result.IndexLength,
			"table_rows":   result.TableRows,
			"total_size":   result.DataLength + result.IndexLength,
		}
	}

	return sizes, nil
}
