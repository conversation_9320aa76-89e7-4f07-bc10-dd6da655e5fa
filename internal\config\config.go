package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Database DatabaseConfig `mapstructure:"database"`
	RabbitMQ RabbitMQConfig `mapstructure:"rabbitmq"`
	API      APIConfig      `mapstructure:"api"`
	Logger   LoggerConfig   `mapstructure:"logger"`
	JS       JSConfig       `mapstructure:"js"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string `mapstructure:"driver"`
	DSN             string `mapstructure:"dsn"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// RabbitMQConfig RabbitMQ配置
type RabbitMQConfig struct {
	URL              string `mapstructure:"url"`
	Exchange         string `mapstructure:"exchange"`
	ExchangeType     string `mapstructure:"exchange_type"`
	DeadLetterExchange string `mapstructure:"dead_letter_exchange"`
	PrefetchCount    int    `mapstructure:"prefetch_count"`
	ReconnectDelay   int    `mapstructure:"reconnect_delay"`
}

// APIConfig API服务配置
type APIConfig struct {
	Port         int    `mapstructure:"port"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
	JWTSecret    string `mapstructure:"jwt_secret"`
}

// LoggerConfig 日志配置
type LoggerConfig struct {
	Level      string `mapstructure:"level"`
	Format     string `mapstructure:"format"`
	OutputPath string `mapstructure:"output_path"`
}

// JSConfig JavaScript引擎配置
type JSConfig struct {
	Timeout     int    `mapstructure:"timeout"`
	MemoryLimit string `mapstructure:"memory_limit"`
}

// Load 加载配置
func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/messageweaver")

	// 设置环境变量前缀
	viper.SetEnvPrefix("MW")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// 数据库默认配置
	viper.SetDefault("database.driver", "mysql")
	viper.SetDefault("database.dsn", "root:password@tcp(localhost:3306)/messageweaver?charset=utf8mb4&parseTime=True&loc=Local")
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("database.max_idle_conns", 10)
	viper.SetDefault("database.conn_max_lifetime", 3600)

	// RabbitMQ默认配置
	viper.SetDefault("rabbitmq.url", "amqp://guest:guest@localhost:5672/")
	viper.SetDefault("rabbitmq.exchange", "events.exchange")
	viper.SetDefault("rabbitmq.exchange_type", "topic")
	viper.SetDefault("rabbitmq.dead_letter_exchange", "dlx.exchange")
	viper.SetDefault("rabbitmq.prefetch_count", 10)
	viper.SetDefault("rabbitmq.reconnect_delay", 5)

	// API默认配置
	viper.SetDefault("api.port", 8080)
	viper.SetDefault("api.read_timeout", 30)
	viper.SetDefault("api.write_timeout", 30)
	viper.SetDefault("api.jwt_secret", "messageweaver-secret")

	// 日志默认配置
	viper.SetDefault("logger.level", "info")
	viper.SetDefault("logger.format", "json")
	viper.SetDefault("logger.output_path", "stdout")

	// JS引擎默认配置
	viper.SetDefault("js.timeout", 5000)
	viper.SetDefault("js.memory_limit", "64MB")
}
