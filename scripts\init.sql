-- MessageWeaver 数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS messageweaver 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE messageweaver;

-- 创建消息处理器表
CREATE TABLE IF NOT EXISTS message_handlers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE COMMENT '处理器名称',
    mq_names JSON NOT NULL COMMENT 'MQ队列名称列表',
    code LONGTEXT NOT NULL COMMENT 'JS处理脚本',
    enabled BOOLEAN DEFAULT true COMMENT '启用状态',
    description TEXT COMMENT '描述',
    data_proxy_names JSON NOT NULL COMMENT 'DataProxy名称列表',
    version INT DEFAULT 1 COMMENT '脚本版本',
    author VARCHAR(255) COMMENT '作者',
    config JSON COMMENT '扩展配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_enabled (enabled),
    INDEX idx_author (author),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息处理器配置表';

-- 创建数据代理表
CREATE TABLE IF NOT EXISTS data_proxies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('mysql', 'dm', 'http') NOT NULL COMMENT '代理类型',
    name VARCHAR(255) NOT NULL UNIQUE COMMENT '代理名称',
    enabled BOOLEAN DEFAULT true COMMENT '启用状态',
    description TEXT COMMENT '描述',
    config JSON NOT NULL COMMENT '配置信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_type (type),
    INDEX idx_enabled (enabled),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据代理配置表';

-- 创建消息处理日志表
CREATE TABLE IF NOT EXISTS message_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(255) NOT NULL COMMENT '消息ID',
    handler_name VARCHAR(255) NOT NULL COMMENT '处理器名称',
    mq_name VARCHAR(255) NOT NULL COMMENT 'MQ队列名',
    event_type VARCHAR(255) NOT NULL COMMENT '事件类型',
    source VARCHAR(255) NOT NULL COMMENT '消息源',
    status ENUM('processing', 'success', 'failed', 'retry') DEFAULT 'processing' COMMENT '处理状态',
    execution_time INT COMMENT '执行时间(ms)',
    memory_used VARCHAR(20) COMMENT '内存使用',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    correlation_id VARCHAR(255) COMMENT '关联ID',
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_message_id (message_id),
    INDEX idx_handler_name (handler_name),
    INDEX idx_status (status),
    INDEX idx_event_type (event_type),
    INDEX idx_correlation_id (correlation_id),
    INDEX idx_processed_at (processed_at),
    INDEX idx_deleted_at (deleted_at),
    INDEX idx_handler_status (handler_name, status),
    INDEX idx_event_type_processed (event_type, processed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息处理日志表';

-- 创建系统指标表
CREATE TABLE IF NOT EXISTS system_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(255) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(20,6) NOT NULL COMMENT '指标值',
    tags JSON COMMENT '标签',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_name (metric_name),
    INDEX idx_timestamp (timestamp),
    INDEX idx_metric_name_timestamp (metric_name, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统指标表';

-- 插入示例数据
INSERT INTO message_handlers (name, mq_names, code, enabled, description, data_proxy_names, author, config) VALUES
('order-analytics-handler', 
 '["order.created.prod"]', 
 'function process(message, context) {\n    const { data } = message;\n    const { logger, utils } = context;\n    \n    if (!data.orderId || !data.totalAmount) {\n        logger.warn("Missing required fields", { messageId: message.messageId });\n        return null;\n    }\n    \n    return {\n        order_id: data.orderId,\n        total_amount: data.totalAmount,\n        customer_id: data.customerId,\n        processed_at: utils.now(),\n        message_id: message.messageId\n    };\n}',
 true,
 '订单创建事件分析处理器',
 '["mysql-analytics-db"]',
 'system',
 '{"timeout": 5000, "memoryLimit": "64MB"}'
),
('user-behavior-handler',
 '["user.action.prod"]',
 'function process(message, context) {\n    const { data } = message;\n    const { logger, utils } = context;\n    \n    if (!data.userId || !data.action) {\n        return null;\n    }\n    \n    return {\n        user_id: data.userId,\n        action: data.action,\n        session_id: data.sessionId,\n        processed_at: utils.now()\n    };\n}',
 true,
 '用户行为事件处理器',
 '["mysql-analytics-db", "analytics-api"]',
 'system',
 '{"timeout": 3000, "memoryLimit": "32MB"}'
);

INSERT INTO data_proxies (type, name, enabled, description, config) VALUES
('mysql', 
 'mysql-analytics-db',
 true,
 '数据分析数据库代理',
 '{"dsn": "root:password@tcp(localhost:3306)/analyticsdb?charset=utf8mb4&parseTime=True&loc=Local", "table": "analytics_events", "maxOpenConns": 10, "maxIdleConns": 5, "connMaxLifetime": 3600}'
),
('http',
 'analytics-api',
 true,
 '数据分析API代理',
 '{"url": "https://analytics.example.com/api/events", "method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer token"}, "timeout": 10000, "retries": 3}'
),
('mysql',
 'mysql-main-db',
 true,
 '主数据库代理',
 '{"dsn": "root:password@tcp(localhost:3306)/maindb?charset=utf8mb4&parseTime=True&loc=Local", "table": "events", "maxOpenConns": 20, "maxIdleConns": 10, "connMaxLifetime": 3600}'
);

-- 创建用于性能优化的视图
CREATE OR REPLACE VIEW v_handler_statistics AS
SELECT 
    h.name as handler_name,
    h.enabled,
    COUNT(l.id) as total_messages,
    SUM(CASE WHEN l.status = 'success' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN l.status = 'failed' THEN 1 ELSE 0 END) as failed_count,
    AVG(CASE WHEN l.execution_time > 0 THEN l.execution_time ELSE NULL END) as avg_execution_time,
    MAX(l.processed_at) as last_processed_at
FROM message_handlers h
LEFT JOIN message_logs l ON h.name = l.handler_name
WHERE h.deleted_at IS NULL
GROUP BY h.id, h.name, h.enabled;

-- 创建用于监控的视图
CREATE OR REPLACE VIEW v_recent_errors AS
SELECT 
    l.id,
    l.message_id,
    l.handler_name,
    l.event_type,
    l.error_message,
    l.retry_count,
    l.processed_at
FROM message_logs l
WHERE l.status = 'failed' 
    AND l.processed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY l.processed_at DESC;

-- 创建存储过程：清理旧数据
DELIMITER //
CREATE PROCEDURE CleanupOldData(IN days_to_keep INT)
BEGIN
    DECLARE cutoff_date TIMESTAMP;
    SET cutoff_date = DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 清理旧的消息日志
    DELETE FROM message_logs WHERE created_at < cutoff_date;
    
    -- 清理旧的系统指标
    DELETE FROM system_metrics WHERE timestamp < cutoff_date;
    
    -- 优化表
    OPTIMIZE TABLE message_logs;
    OPTIMIZE TABLE system_metrics;
END //
DELIMITER ;

-- 创建存储过程：获取处理器性能报告
DELIMITER //
CREATE PROCEDURE GetHandlerPerformanceReport(IN handler_name_param VARCHAR(255), IN start_date TIMESTAMP, IN end_date TIMESTAMP)
BEGIN
    SELECT 
        DATE(processed_at) as date,
        COUNT(*) as total_messages,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
        AVG(CASE WHEN execution_time > 0 THEN execution_time ELSE NULL END) as avg_execution_time,
        MIN(execution_time) as min_execution_time,
        MAX(execution_time) as max_execution_time
    FROM message_logs
    WHERE handler_name = handler_name_param
        AND processed_at BETWEEN start_date AND end_date
    GROUP BY DATE(processed_at)
    ORDER BY date;
END //
DELIMITER ;

-- 创建事件：定期清理数据（每天凌晨2点执行）
CREATE EVENT IF NOT EXISTS cleanup_old_data
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY + INTERVAL 2 HOUR)
DO
    CALL CleanupOldData(30);  -- 保留30天的数据
