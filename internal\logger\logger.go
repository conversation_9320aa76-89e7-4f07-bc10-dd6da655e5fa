package logger

import (
	"os"

	"messageweaver/internal/config"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger 日志器接口
type Logger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Fatal(msg string, fields ...zap.Field)
	Sync() error
	With(fields ...zap.Field) Logger
}

// zapLogger zap日志器实现
type zapLogger struct {
	*zap.Logger
}

// New 创建新的日志器
func New(cfg config.LoggerConfig) (Logger, error) {
	// 设置日志级别
	level := zapcore.InfoLevel
	switch cfg.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	case "fatal":
		level = zapcore.FatalLevel
	}

	// 设置编码器配置
	var encoderConfig zapcore.EncoderConfig
	if cfg.Format == "json" {
		encoderConfig = zap.NewProductionEncoderConfig()
	} else {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
	}

	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 设置编码器
	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 设置输出
	var writeSyncer zapcore.WriteSyncer
	if cfg.OutputPath == "stdout" || cfg.OutputPath == "" {
		writeSyncer = zapcore.AddSync(os.Stdout)
	} else {
		file, err := os.OpenFile(cfg.OutputPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, err
		}
		writeSyncer = zapcore.AddSync(file)
	}

	// 创建核心
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// 创建logger
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return &zapLogger{Logger: logger}, nil
}

// With 添加字段
func (l *zapLogger) With(fields ...zap.Field) Logger {
	return &zapLogger{Logger: l.Logger.With(fields...)}
}

// JSLogger JavaScript日志器实现
type JSLogger struct {
	logger Logger
	fields map[string]interface{}
}

// NewJSLogger 创建JavaScript日志器
func NewJSLogger(logger Logger) *JSLogger {
	return &JSLogger{
		logger: logger,
		fields: make(map[string]interface{}),
	}
}

// Debug 调试日志
func (jl *JSLogger) Debug(message string, data map[string]interface{}) {
	fields := jl.buildFields(data)
	jl.logger.Debug(message, fields...)
}

// Info 信息日志
func (jl *JSLogger) Info(message string, data map[string]interface{}) {
	fields := jl.buildFields(data)
	jl.logger.Info(message, fields...)
}

// Warn 警告日志
func (jl *JSLogger) Warn(message string, data map[string]interface{}) {
	fields := jl.buildFields(data)
	jl.logger.Warn(message, fields...)
}

// Error 错误日志
func (jl *JSLogger) Error(message string, data map[string]interface{}) {
	fields := jl.buildFields(data)
	jl.logger.Error(message, fields...)
}

// buildFields 构建zap字段
func (jl *JSLogger) buildFields(data map[string]interface{}) []zap.Field {
	var fields []zap.Field
	
	// 添加固定字段
	for k, v := range jl.fields {
		fields = append(fields, zap.Any(k, v))
	}
	
	// 添加动态字段
	if data != nil {
		for k, v := range data {
			fields = append(fields, zap.Any(k, v))
		}
	}
	
	return fields
}

// SetField 设置固定字段
func (jl *JSLogger) SetField(key string, value interface{}) {
	jl.fields[key] = value
}

// SetFields 设置多个固定字段
func (jl *JSLogger) SetFields(fields map[string]interface{}) {
	for k, v := range fields {
		jl.fields[k] = v
	}
}

// Clone 克隆日志器
func (jl *JSLogger) Clone() *JSLogger {
	clone := &JSLogger{
		logger: jl.logger,
		fields: make(map[string]interface{}),
	}
	
	for k, v := range jl.fields {
		clone.fields[k] = v
	}
	
	return clone
}

// WithContext 添加上下文信息
func (jl *JSLogger) WithContext(handlerName, messageId string) *JSLogger {
	clone := jl.Clone()
	clone.SetField("handler", handlerName)
	clone.SetField("message_id", messageId)
	return clone
}
