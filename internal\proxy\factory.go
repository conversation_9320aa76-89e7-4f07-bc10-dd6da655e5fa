package proxy

import (
	"fmt"

	"messageweaver/internal/logger"
	"messageweaver/internal/models"
)

// DefaultFactory 默认代理工厂
type DefaultFactory struct {
	logger logger.Logger
}

// NewDefaultFactory 创建默认代理工厂
func NewDefaultFactory(logger logger.Logger) *DefaultFactory {
	return &DefaultFactory{
		logger: logger,
	}
}

// CreateProxy 创建代理
func (f *DefaultFactory) CreateProxy(config *models.DataProxy) (DataProxy, error) {
	switch config.Type {
	case models.ProxyTypeMySQL:
		return NewMySQLProxy(config, f.logger)
	case models.ProxyTypeDM:
		return NewDMProxy(config, f.logger)
	case models.ProxyTypeHTTP:
		return NewHTTPProxy(config, f.logger)
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", config.Type)
	}
}

// GetSupportedTypes 获取支持的类型
func (f *DefaultFactory) GetSupportedTypes() []models.DataProxyType {
	return []models.DataProxyType{
		models.ProxyTypeMySQL,
		models.ProxyTypeDM,
		models.ProxyTypeHTTP,
	}
}

// DMProxy 达梦数据库代理（继承MySQL代理的大部分功能）
type DMProxy struct {
	*MySQLProxy
}

// NewDMProxy 创建达梦数据库代理
func NewDMProxy(config *models.DataProxy, logger logger.Logger) (*DMProxy, error) {
	if config.Type != models.ProxyTypeDM {
		return nil, fmt.Errorf("invalid proxy type: %s", config.Type)
	}

	// 达梦数据库使用类似MySQL的接口，但驱动不同
	// 这里为了简化，暂时复用MySQL的实现
	// 实际项目中需要使用达梦专用的驱动
	
	// 临时将类型改为MySQL以复用代码
	tempConfig := *config
	tempConfig.Type = models.ProxyTypeMySQL
	
	mysqlProxy, err := NewMySQLProxy(&tempConfig, logger)
	if err != nil {
		return nil, err
	}
	
	// 恢复原始类型
	mysqlProxy.config.Type = models.ProxyTypeDM
	
	return &DMProxy{
		MySQLProxy: mysqlProxy,
	}, nil
}

// GetType 获取代理类型
func (p *DMProxy) GetType() models.DataProxyType {
	return models.ProxyTypeDM
}

// TestConnection 测试连接（达梦特定实现）
func (p *DMProxy) TestConnection() error {
	// 这里可以添加达梦数据库特定的连接测试逻辑
	return p.MySQLProxy.TestConnection()
}

// GetStatus 获取状态
func (p *DMProxy) GetStatus() map[string]interface{} {
	status := p.MySQLProxy.GetStatus()
	status["type"] = models.ProxyTypeDM
	return status
}

// ProxyBuilder 代理构建器
type ProxyBuilder struct {
	config  *models.DataProxy
	logger  logger.Logger
	factory ProxyFactory
}

// NewProxyBuilder 创建代理构建器
func NewProxyBuilder() *ProxyBuilder {
	return &ProxyBuilder{}
}

// WithConfig 设置配置
func (b *ProxyBuilder) WithConfig(config *models.DataProxy) *ProxyBuilder {
	b.config = config
	return b
}

// WithLogger 设置日志器
func (b *ProxyBuilder) WithLogger(logger logger.Logger) *ProxyBuilder {
	b.logger = logger
	return b
}

// WithFactory 设置工厂
func (b *ProxyBuilder) WithFactory(factory ProxyFactory) *ProxyBuilder {
	b.factory = factory
	return b
}

// Build 构建代理
func (b *ProxyBuilder) Build() (DataProxy, error) {
	if b.config == nil {
		return nil, fmt.Errorf("config is required")
	}
	
	if b.logger == nil {
		return nil, fmt.Errorf("logger is required")
	}
	
	if b.factory == nil {
		b.factory = NewDefaultFactory(b.logger)
	}
	
	return b.factory.CreateProxy(b.config)
}

// ValidateConfig 验证代理配置
func ValidateConfig(config *models.DataProxy) error {
	if config.Name == "" {
		return fmt.Errorf("proxy name is required")
	}
	
	if config.Type == "" {
		return fmt.Errorf("proxy type is required")
	}
	
	switch config.Type {
	case models.ProxyTypeMySQL, models.ProxyTypeDM:
		if config.GetDSN() == "" {
			return fmt.Errorf("DSN is required for database proxy")
		}
		if config.GetTable() == "" {
			return fmt.Errorf("table is required for database proxy")
		}
	case models.ProxyTypeHTTP:
		if config.GetURL() == "" {
			return fmt.Errorf("URL is required for HTTP proxy")
		}
	default:
		return fmt.Errorf("unsupported proxy type: %s", config.Type)
	}
	
	return nil
}

// CreateProxyFromConfig 从配置创建代理的便捷函数
func CreateProxyFromConfig(config *models.DataProxy, logger logger.Logger) (DataProxy, error) {
	if err := ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}
	
	return NewProxyBuilder().
		WithConfig(config).
		WithLogger(logger).
		Build()
}

// GetProxyTypeDescription 获取代理类型描述
func GetProxyTypeDescription(proxyType models.DataProxyType) string {
	switch proxyType {
	case models.ProxyTypeMySQL:
		return "MySQL Database Proxy"
	case models.ProxyTypeDM:
		return "DM Database Proxy"
	case models.ProxyTypeHTTP:
		return "HTTP API Proxy"
	default:
		return "Unknown Proxy Type"
	}
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig(proxyType models.DataProxyType, name string) *models.DataProxy {
	config := &models.DataProxy{
		Type:        proxyType,
		Name:        name,
		Enabled:     true,
		Description: GetProxyTypeDescription(proxyType),
		Config:      make(map[string]interface{}),
	}
	
	switch proxyType {
	case models.ProxyTypeMySQL:
		config.Config["maxOpenConns"] = 10
		config.Config["maxIdleConns"] = 5
		config.Config["connMaxLifetime"] = 3600
	case models.ProxyTypeDM:
		config.Config["maxOpenConns"] = 10
		config.Config["maxIdleConns"] = 5
		config.Config["connMaxLifetime"] = 3600
	case models.ProxyTypeHTTP:
		config.Config["method"] = "POST"
		config.Config["timeout"] = 10000
		config.Config["headers"] = map[string]string{
			"Content-Type": "application/json",
		}
	}
	
	return config
}
