package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"messageweaver/internal/api"
	"messageweaver/internal/config"
	"messageweaver/internal/database"
	"messageweaver/internal/handler"
	"messageweaver/internal/logger"
	"messageweaver/internal/mq"
	"messageweaver/internal/proxy"

	"go.uber.org/zap"
)

func main() {
	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger, err := logger.New(cfg.Logger)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Sync()

	logger.Info("Starting MessageWeaver", zap.String("version", "1.0.0"))

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		logger.Fatal("Failed to initialize database", zap.Error(err))
	}

	// 初始化代理管理器
	proxyManager := proxy.NewManager(logger)

	// 初始化处理器管理器
	handlerManager := handler.NewManager(logger, proxyManager)

	// 初始化MQ消费者
	mqConsumer, err := mq.NewConsumer(cfg.RabbitMQ, logger, handlerManager)
	if err != nil {
		logger.Fatal("Failed to initialize MQ consumer", zap.Error(err))
	}

	// 初始化API服务器
	apiServer := api.NewServer(cfg.API, logger, db, handlerManager, proxyManager)

	// 启动服务
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动MQ消费者
	go func() {
		if err := mqConsumer.Start(ctx); err != nil {
			logger.Error("MQ consumer error", zap.Error(err))
		}
	}()

	// 启动API服务器
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.API.Port),
		Handler: apiServer.Router(),
	}

	go func() {
		logger.Info("Starting API server", zap.Int("port", cfg.API.Port))
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start API server", zap.Error(err))
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Shutting down server...")

	// 优雅关闭
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 停止MQ消费者
	cancel()

	// 停止API服务器
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Error("Server forced to shutdown", zap.Error(err))
	}

	// 关闭数据库连接
	if sqlDB, err := db.DB(); err == nil {
		sqlDB.Close()
	}

	logger.Info("Server exited")
}
