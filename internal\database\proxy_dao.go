package database

import (
	"messageweaver/internal/models"
)

// ProxyDAO DataProxy数据访问对象
type ProxyDAO struct {
	db *Database
}

// NewProxyDAO 创建ProxyDAO
func NewProxyDAO(db *Database) *ProxyDAO {
	return &ProxyDAO{db: db}
}

// Create 创建代理
func (dao *ProxyDAO) Create(proxy *models.DataProxy) error {
	// 检查名称是否已存在
	var count int64
	if err := dao.db.Model(&models.DataProxy{}).Where("name = ?", proxy.Name).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return models.ErrProxyAlreadyExists
	}

	return dao.db.Create(proxy).Error
}

// GetByID 根据ID获取代理
func (dao *ProxyDAO) GetByID(id uint) (*models.DataProxy, error) {
	var proxy models.DataProxy
	err := dao.db.First(&proxy, id).Error
	if err != nil {
		if dao.db.IsRecordNotFound(err) {
			return nil, models.ErrProxyNotFound
		}
		return nil, err
	}
	return &proxy, nil
}

// GetByName 根据名称获取代理
func (dao *ProxyDAO) GetByName(name string) (*models.DataProxy, error) {
	var proxy models.DataProxy
	err := dao.db.Where("name = ?", name).First(&proxy).Error
	if err != nil {
		if dao.db.IsRecordNotFound(err) {
			return nil, models.ErrProxyNotFound
		}
		return nil, err
	}
	return &proxy, nil
}

// Update 更新代理
func (dao *ProxyDAO) Update(proxy *models.DataProxy) error {
	// 检查是否存在
	var existing models.DataProxy
	if err := dao.db.First(&existing, proxy.ID).Error; err != nil {
		if dao.db.IsRecordNotFound(err) {
			return models.ErrProxyNotFound
		}
		return err
	}

	// 检查名称冲突（排除自己）
	var count int64
	if err := dao.db.Model(&models.DataProxy{}).
		Where("name = ? AND id != ?", proxy.Name, proxy.ID).
		Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return models.ErrProxyAlreadyExists
	}

	return dao.db.Save(proxy).Error
}

// Delete 删除代理
func (dao *ProxyDAO) Delete(id uint) error {
	result := dao.db.Delete(&models.DataProxy{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrProxyNotFound
	}
	return nil
}

// DeleteByName 根据名称删除代理
func (dao *ProxyDAO) DeleteByName(name string) error {
	result := dao.db.Where("name = ?", name).Delete(&models.DataProxy{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrProxyNotFound
	}
	return nil
}

// List 获取代理列表
func (dao *ProxyDAO) List(offset, limit int, proxyType *models.DataProxyType, enabled *bool) ([]*models.DataProxy, int64, error) {
	var proxies []*models.DataProxy
	var total int64

	query := dao.db.Model(&models.DataProxy{})
	
	// 添加过滤条件
	if proxyType != nil {
		query = query.Where("type = ?", *proxyType)
	}
	if enabled != nil {
		query = query.Where("enabled = ?", *enabled)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&proxies).Error; err != nil {
		return nil, 0, err
	}

	return proxies, total, nil
}

// GetByNames 根据名称列表获取代理
func (dao *ProxyDAO) GetByNames(names []string) ([]*models.DataProxy, error) {
	var proxies []*models.DataProxy
	err := dao.db.Where("name IN ? AND enabled = ?", names, true).Find(&proxies).Error
	return proxies, err
}

// GetEnabledProxies 获取所有启用的代理
func (dao *ProxyDAO) GetEnabledProxies() ([]*models.DataProxy, error) {
	var proxies []*models.DataProxy
	err := dao.db.Where("enabled = ?", true).Find(&proxies).Error
	return proxies, err
}

// GetByType 根据类型获取代理
func (dao *ProxyDAO) GetByType(proxyType models.DataProxyType) ([]*models.DataProxy, error) {
	var proxies []*models.DataProxy
	err := dao.db.Where("type = ? AND enabled = ?", proxyType, true).Find(&proxies).Error
	return proxies, err
}

// UpdateStatus 更新代理状态
func (dao *ProxyDAO) UpdateStatus(id uint, enabled bool) error {
	result := dao.db.Model(&models.DataProxy{}).Where("id = ?", id).Update("enabled", enabled)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrProxyNotFound
	}
	return nil
}

// UpdateStatusByName 根据名称更新代理状态
func (dao *ProxyDAO) UpdateStatusByName(name string, enabled bool) error {
	result := dao.db.Model(&models.DataProxy{}).Where("name = ?", name).Update("enabled", enabled)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrProxyNotFound
	}
	return nil
}

// Search 搜索代理
func (dao *ProxyDAO) Search(keyword string, offset, limit int) ([]*models.DataProxy, int64, error) {
	var proxies []*models.DataProxy
	var total int64

	query := dao.db.Model(&models.DataProxy{}).Where(
		"name LIKE ? OR description LIKE ?",
		"%"+keyword+"%", "%"+keyword+"%",
	)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&proxies).Error; err != nil {
		return nil, 0, err
	}

	return proxies, total, nil
}

// GetStatistics 获取代理统计信息
func (dao *ProxyDAO) GetStatistics() (map[string]interface{}, error) {
	var stats []struct {
		Type     models.DataProxyType `gorm:"column:type"`
		Total    int64               `gorm:"column:total"`
		Enabled  int64               `gorm:"column:enabled"`
		Disabled int64               `gorm:"column:disabled"`
	}

	err := dao.db.Model(&models.DataProxy{}).Select(`
		type,
		COUNT(*) as total,
		SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled,
		SUM(CASE WHEN enabled = 0 THEN 1 ELSE 0 END) as disabled
	`).Group("type").Scan(&stats).Error

	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	var totalCount, enabledCount, disabledCount int64

	for _, stat := range stats {
		result[string(stat.Type)] = map[string]interface{}{
			"total":    stat.Total,
			"enabled":  stat.Enabled,
			"disabled": stat.Disabled,
		}
		totalCount += stat.Total
		enabledCount += stat.Enabled
		disabledCount += stat.Disabled
	}

	result["summary"] = map[string]interface{}{
		"total":    totalCount,
		"enabled":  enabledCount,
		"disabled": disabledCount,
	}

	return result, nil
}

// BatchUpdateStatus 批量更新状态
func (dao *ProxyDAO) BatchUpdateStatus(ids []uint, enabled bool) error {
	return dao.db.Model(&models.DataProxy{}).Where("id IN ?", ids).Update("enabled", enabled).Error
}

// TestConnection 测试代理连接
func (dao *ProxyDAO) TestConnection(id uint) error {
	proxy, err := dao.GetByID(id)
	if err != nil {
		return err
	}

	// 这里可以根据代理类型进行实际的连接测试
	// 暂时只是检查配置是否完整
	switch proxy.Type {
	case models.ProxyTypeMySQL, models.ProxyTypeDM:
		if proxy.GetDSN() == "" {
			return models.ErrMissingDSN
		}
		if proxy.GetTable() == "" {
			return models.ErrMissingTable
		}
	case models.ProxyTypeHTTP:
		if proxy.GetURL() == "" {
			return models.ErrMissingURL
		}
	}

	return nil
}
