package models

import (
	"time"

	"gorm.io/gorm"
)

// MessageLogStatus 消息处理状态
type MessageLogStatus string

const (
	StatusProcessing MessageLogStatus = "processing"
	StatusSuccess    MessageLogStatus = "success"
	StatusFailed     MessageLogStatus = "failed"
	StatusRetry      MessageLogStatus = "retry"
)

// MessageLog 消息处理日志模型
type MessageLog struct {
	ID            uint             `json:"id" gorm:"primaryKey"`
	MessageId     string           `json:"messageId" gorm:"index;not null"`
	HandlerName   string           `json:"handlerName" gorm:"index;not null"`
	MqName        string           `json:"mqName" gorm:"index;not null"`
	EventType     string           `json:"eventType" gorm:"index;not null"`
	Source        string           `json:"source" gorm:"index;not null"`
	Status        MessageLogStatus `json:"status" gorm:"index;default:'processing'"`
	ExecutionTime int              `json:"executionTime"` // 执行时间(ms)
	MemoryUsed    string           `json:"memoryUsed"`    // 内存使用
	ErrorMessage  string           `json:"errorMessage" gorm:"type:text"`
	RetryCount    int              `json:"retryCount" gorm:"default:0"`
	CorrelationId string           `json:"correlationId" gorm:"index"`
	ProcessedAt   time.Time        `json:"processedAt"`
	CreatedAt     time.Time        `json:"createdAt"`
	UpdatedAt     time.Time        `json:"updatedAt"`
	DeletedAt     gorm.DeletedAt   `json:"-" gorm:"index"`
}

// TableName 指定表名
func (MessageLog) TableName() string {
	return "message_logs"
}

// SystemMetric 系统指标模型
type SystemMetric struct {
	ID          uint                   `json:"id" gorm:"primaryKey"`
	MetricName  string                 `json:"metricName" gorm:"index;not null"`
	MetricValue float64                `json:"metricValue" gorm:"not null"`
	Tags        map[string]interface{} `json:"tags" gorm:"type:json"`
	Timestamp   time.Time              `json:"timestamp" gorm:"index"`
	CreatedAt   time.Time              `json:"createdAt"`
}

// TableName 指定表名
func (SystemMetric) TableName() string {
	return "system_metrics"
}

// IsSuccess 判断是否处理成功
func (l *MessageLog) IsSuccess() bool {
	return l.Status == StatusSuccess
}

// IsFailed 判断是否处理失败
func (l *MessageLog) IsFailed() bool {
	return l.Status == StatusFailed
}

// IsProcessing 判断是否正在处理
func (l *MessageLog) IsProcessing() bool {
	return l.Status == StatusProcessing
}

// IsRetrying 判断是否正在重试
func (l *MessageLog) IsRetrying() bool {
	return l.Status == StatusRetry
}

// MarkSuccess 标记为成功
func (l *MessageLog) MarkSuccess(executionTime int, memoryUsed string) {
	l.Status = StatusSuccess
	l.ExecutionTime = executionTime
	l.MemoryUsed = memoryUsed
	l.ProcessedAt = time.Now()
}

// MarkFailed 标记为失败
func (l *MessageLog) MarkFailed(errorMessage string, executionTime int) {
	l.Status = StatusFailed
	l.ErrorMessage = errorMessage
	l.ExecutionTime = executionTime
	l.ProcessedAt = time.Now()
}

// MarkRetry 标记为重试
func (l *MessageLog) MarkRetry(errorMessage string) {
	l.Status = StatusRetry
	l.ErrorMessage = errorMessage
	l.RetryCount++
	l.ProcessedAt = time.Now()
}

// ToMap 转换为map（用于API响应）
func (l *MessageLog) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":            l.ID,
		"messageId":     l.MessageId,
		"handlerName":   l.HandlerName,
		"mqName":        l.MqName,
		"eventType":     l.EventType,
		"source":        l.Source,
		"status":        l.Status,
		"executionTime": l.ExecutionTime,
		"memoryUsed":    l.MemoryUsed,
		"errorMessage":  l.ErrorMessage,
		"retryCount":    l.RetryCount,
		"correlationId": l.CorrelationId,
		"processedAt":   l.ProcessedAt,
		"createdAt":     l.CreatedAt,
		"updatedAt":     l.UpdatedAt,
	}
}

// CreateMessageLog 创建消息日志
func CreateMessageLog(messageId, handlerName, mqName, eventType, source, correlationId string) *MessageLog {
	return &MessageLog{
		MessageId:     messageId,
		HandlerName:   handlerName,
		MqName:        mqName,
		EventType:     eventType,
		Source:        source,
		Status:        StatusProcessing,
		CorrelationId: correlationId,
		ProcessedAt:   time.Now(),
	}
}

// CreateSystemMetric 创建系统指标
func CreateSystemMetric(name string, value float64, tags map[string]interface{}) *SystemMetric {
	return &SystemMetric{
		MetricName:  name,
		MetricValue: value,
		Tags:        tags,
		Timestamp:   time.Now(),
	}
}
