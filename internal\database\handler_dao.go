package database

import (
	"fmt"

	"messageweaver/internal/models"

	"gorm.io/gorm"
)

// HandlerDAO MessageHandler数据访问对象
type HandlerDAO struct {
	db *Database
}

// NewHandlerDAO 创建HandlerDAO
func NewHandlerDAO(db *Database) *HandlerDAO {
	return &HandlerDAO{db: db}
}

// Create 创建处理器
func (dao *HandlerDAO) Create(handler *models.MessageHandler) error {
	if err := handler.Validate(); err != nil {
		return err
	}

	// 检查名称是否已存在
	var count int64
	if err := dao.db.Model(&models.MessageHandler{}).Where("name = ?", handler.Name).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return models.ErrHandlerAlreadyExists
	}

	return dao.db.Create(handler).Error
}

// GetByID 根据ID获取处理器
func (dao *HandlerDAO) GetByID(id uint) (*models.MessageHandler, error) {
	var handler models.MessageHandler
	err := dao.db.First(&handler, id).Error
	if err != nil {
		if dao.db.IsRecordNotFound(err) {
			return nil, models.ErrHandlerNotFound
		}
		return nil, err
	}
	return &handler, nil
}

// GetByName 根据名称获取处理器
func (dao *HandlerDAO) GetByName(name string) (*models.MessageHandler, error) {
	var handler models.MessageHandler
	err := dao.db.Where("name = ?", name).First(&handler).Error
	if err != nil {
		if dao.db.IsRecordNotFound(err) {
			return nil, models.ErrHandlerNotFound
		}
		return nil, err
	}
	return &handler, nil
}

// Update 更新处理器
func (dao *HandlerDAO) Update(handler *models.MessageHandler) error {
	if err := handler.Validate(); err != nil {
		return err
	}

	// 检查是否存在
	var existing models.MessageHandler
	if err := dao.db.First(&existing, handler.ID).Error; err != nil {
		if dao.db.IsRecordNotFound(err) {
			return models.ErrHandlerNotFound
		}
		return err
	}

	// 检查名称冲突（排除自己）
	var count int64
	if err := dao.db.Model(&models.MessageHandler{}).
		Where("name = ? AND id != ?", handler.Name, handler.ID).
		Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return models.ErrHandlerAlreadyExists
	}

	return dao.db.Save(handler).Error
}

// Delete 删除处理器
func (dao *HandlerDAO) Delete(id uint) error {
	result := dao.db.Delete(&models.MessageHandler{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrHandlerNotFound
	}
	return nil
}

// DeleteByName 根据名称删除处理器
func (dao *HandlerDAO) DeleteByName(name string) error {
	result := dao.db.Where("name = ?", name).Delete(&models.MessageHandler{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrHandlerNotFound
	}
	return nil
}

// List 获取处理器列表
func (dao *HandlerDAO) List(offset, limit int, enabled *bool) ([]*models.MessageHandler, int64, error) {
	var handlers []*models.MessageHandler
	var total int64

	query := dao.db.Model(&models.MessageHandler{})
	
	// 添加过滤条件
	if enabled != nil {
		query = query.Where("enabled = ?", *enabled)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&handlers).Error; err != nil {
		return nil, 0, err
	}

	return handlers, total, nil
}

// GetByMqName 根据MQ队列名获取处理器
func (dao *HandlerDAO) GetByMqName(mqName string) ([]*models.MessageHandler, error) {
	var handlers []*models.MessageHandler
	
	// 使用JSON_CONTAINS查询包含指定MQ名称的处理器
	err := dao.db.Where("enabled = ? AND JSON_CONTAINS(mq_names, ?)", true, fmt.Sprintf(`"%s"`, mqName)).
		Find(&handlers).Error
	
	if err != nil {
		return nil, err
	}

	return handlers, nil
}

// GetEnabledHandlers 获取所有启用的处理器
func (dao *HandlerDAO) GetEnabledHandlers() ([]*models.MessageHandler, error) {
	var handlers []*models.MessageHandler
	err := dao.db.Where("enabled = ?", true).Find(&handlers).Error
	return handlers, err
}

// UpdateStatus 更新处理器状态
func (dao *HandlerDAO) UpdateStatus(id uint, enabled bool) error {
	result := dao.db.Model(&models.MessageHandler{}).Where("id = ?", id).Update("enabled", enabled)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrHandlerNotFound
	}
	return nil
}

// UpdateStatusByName 根据名称更新处理器状态
func (dao *HandlerDAO) UpdateStatusByName(name string, enabled bool) error {
	result := dao.db.Model(&models.MessageHandler{}).Where("name = ?", name).Update("enabled", enabled)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return models.ErrHandlerNotFound
	}
	return nil
}

// Search 搜索处理器
func (dao *HandlerDAO) Search(keyword string, offset, limit int) ([]*models.MessageHandler, int64, error) {
	var handlers []*models.MessageHandler
	var total int64

	query := dao.db.Model(&models.MessageHandler{}).Where(
		"name LIKE ? OR description LIKE ? OR author LIKE ?",
		"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%",
	)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据
	if err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&handlers).Error; err != nil {
		return nil, 0, err
	}

	return handlers, total, nil
}

// GetStatistics 获取处理器统计信息
func (dao *HandlerDAO) GetStatistics() (map[string]interface{}, error) {
	var stats struct {
		Total    int64 `gorm:"column:total"`
		Enabled  int64 `gorm:"column:enabled"`
		Disabled int64 `gorm:"column:disabled"`
	}

	err := dao.db.Model(&models.MessageHandler{}).Select(`
		COUNT(*) as total,
		SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as enabled,
		SUM(CASE WHEN enabled = 0 THEN 1 ELSE 0 END) as disabled
	`).Scan(&stats).Error

	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"total":    stats.Total,
		"enabled":  stats.Enabled,
		"disabled": stats.Disabled,
	}, nil
}

// BatchUpdateStatus 批量更新状态
func (dao *HandlerDAO) BatchUpdateStatus(ids []uint, enabled bool) error {
	return dao.db.Model(&models.MessageHandler{}).Where("id IN ?", ids).Update("enabled", enabled).Error
}

// GetVersionHistory 获取版本历史
func (dao *HandlerDAO) GetVersionHistory(name string) ([]*models.MessageHandler, error) {
	var handlers []*models.MessageHandler
	err := dao.db.Unscoped().Where("name = ?", name).Order("version DESC").Find(&handlers).Error
	return handlers, err
}
