# MessageWeaver 配置文件

# 数据库配置
database:
  driver: mysql
  dsn: "root:password@tcp(localhost:3306)/messageweaver?charset=utf8mb4&parseTime=True&loc=Local"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600

# RabbitMQ配置
rabbitmq:
  url: "amqp://guest:guest@localhost:5672/"
  exchange: "events.exchange"
  exchange_type: "topic"
  dead_letter_exchange: "dlx.exchange"
  prefetch_count: 10
  reconnect_delay: 5

# API服务配置
api:
  port: 8080
  read_timeout: 30
  write_timeout: 30
  jwt_secret: "messageweaver-secret"

# 日志配置
logger:
  level: "info"
  format: "json"
  output_path: "stdout"

# JavaScript引擎配置
js:
  timeout: 5000
  memory_limit: "64MB"
