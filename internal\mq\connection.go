package mq

import (
	"fmt"
	"sync"
	"time"

	"messageweaver/internal/config"
	"messageweaver/internal/logger"

	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

// Connection RabbitMQ连接管理器
type Connection struct {
	config     config.RabbitMQConfig
	logger     logger.Logger
	conn       *amqp.Connection
	channel    *amqp.Channel
	mutex      sync.RWMutex
	closed     bool
	reconnectCh chan bool
}

// NewConnection 创建RabbitMQ连接
func NewConnection(cfg config.RabbitMQConfig, logger logger.Logger) (*Connection, error) {
	c := &Connection{
		config:      cfg,
		logger:      logger,
		reconnectCh: make(chan bool, 1),
	}

	if err := c.connect(); err != nil {
		return nil, err
	}

	// 启动重连监听
	go c.handleReconnect()

	return c, nil
}

// connect 建立连接
func (c *Connection) connect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.closed {
		return fmt.Errorf("connection is closed")
	}

	// 关闭现有连接
	if c.conn != nil && !c.conn.IsClosed() {
		c.conn.Close()
	}

	// 建立新连接
	conn, err := amqp.Dial(c.config.URL)
	if err != nil {
		return fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}

	// 创建通道
	channel, err := conn.Channel()
	if err != nil {
		conn.Close()
		return fmt.Errorf("failed to create channel: %w", err)
	}

	// 设置QoS
	if err := channel.Qos(c.config.PrefetchCount, 0, false); err != nil {
		channel.Close()
		conn.Close()
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	c.conn = conn
	c.channel = channel

	c.logger.Info("Connected to RabbitMQ", zap.String("url", c.config.URL))

	// 监听连接关闭
	go c.watchConnection()

	return nil
}

// watchConnection 监听连接状态
func (c *Connection) watchConnection() {
	notifyClose := make(chan *amqp.Error)
	c.conn.NotifyClose(notifyClose)

	select {
	case err := <-notifyClose:
		if err != nil {
			c.logger.Error("RabbitMQ connection closed", zap.Error(err))
		}
		if !c.closed {
			c.triggerReconnect()
		}
	}
}

// triggerReconnect 触发重连
func (c *Connection) triggerReconnect() {
	select {
	case c.reconnectCh <- true:
	default:
	}
}

// handleReconnect 处理重连
func (c *Connection) handleReconnect() {
	for range c.reconnectCh {
		if c.closed {
			return
		}

		c.logger.Info("Attempting to reconnect to RabbitMQ...")

		for !c.closed {
			if err := c.connect(); err != nil {
				c.logger.Error("Failed to reconnect", zap.Error(err))
				time.Sleep(time.Duration(c.config.ReconnectDelay) * time.Second)
				continue
			}
			break
		}
	}
}

// GetChannel 获取通道
func (c *Connection) GetChannel() (*amqp.Channel, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	if c.closed {
		return nil, fmt.Errorf("connection is closed")
	}

	if c.channel == nil || c.channel.IsClosed() {
		return nil, fmt.Errorf("channel is not available")
	}

	return c.channel, nil
}

// IsConnected 检查是否已连接
func (c *Connection) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return c.conn != nil && !c.conn.IsClosed() && c.channel != nil && !c.channel.IsClosed()
}

// Close 关闭连接
func (c *Connection) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.closed = true
	close(c.reconnectCh)

	var err error
	if c.channel != nil && !c.channel.IsClosed() {
		if closeErr := c.channel.Close(); closeErr != nil {
			err = closeErr
		}
	}

	if c.conn != nil && !c.conn.IsClosed() {
		if closeErr := c.conn.Close(); closeErr != nil {
			err = closeErr
		}
	}

	c.logger.Info("RabbitMQ connection closed")
	return err
}

// DeclareExchange 声明交换机
func (c *Connection) DeclareExchange(name, kind string, durable, autoDelete bool) error {
	channel, err := c.GetChannel()
	if err != nil {
		return err
	}

	return channel.ExchangeDeclare(
		name,       // name
		kind,       // type
		durable,    // durable
		autoDelete, // auto-deleted
		false,      // internal
		false,      // no-wait
		nil,        // arguments
	)
}

// DeclareQueue 声明队列
func (c *Connection) DeclareQueue(name string, durable, autoDelete bool, args amqp.Table) (amqp.Queue, error) {
	channel, err := c.GetChannel()
	if err != nil {
		return amqp.Queue{}, err
	}

	return channel.QueueDeclare(
		name,       // name
		durable,    // durable
		autoDelete, // delete when unused
		false,      // exclusive
		false,      // no-wait
		args,       // arguments
	)
}

// BindQueue 绑定队列到交换机
func (c *Connection) BindQueue(queueName, routingKey, exchangeName string) error {
	channel, err := c.GetChannel()
	if err != nil {
		return err
	}

	return channel.QueueBind(
		queueName,    // queue name
		routingKey,   // routing key
		exchangeName, // exchange
		false,        // no-wait
		nil,          // arguments
	)
}

// Publish 发布消息
func (c *Connection) Publish(exchange, routingKey string, msg amqp.Publishing) error {
	channel, err := c.GetChannel()
	if err != nil {
		return err
	}

	return channel.Publish(
		exchange,   // exchange
		routingKey, // routing key
		false,      // mandatory
		false,      // immediate
		msg,        // message
	)
}

// Consume 消费消息
func (c *Connection) Consume(queueName, consumerTag string) (<-chan amqp.Delivery, error) {
	channel, err := c.GetChannel()
	if err != nil {
		return nil, err
	}

	return channel.Consume(
		queueName,   // queue
		consumerTag, // consumer
		false,       // auto-ack
		false,       // exclusive
		false,       // no-local
		false,       // no-wait
		nil,         // args
	)
}

// GetConnectionInfo 获取连接信息
func (c *Connection) GetConnectionInfo() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	info := map[string]interface{}{
		"url":       c.config.URL,
		"connected": c.IsConnected(),
		"closed":    c.closed,
	}

	if c.conn != nil {
		info["local_addr"] = c.conn.LocalAddr().String()
		info["remote_addr"] = c.conn.RemoteAddr().String()
	}

	return info
}
