# Me**MessageWeaver（数据流编织器）** 

一个智能的消息处理和数据流转系统，以Go语言实现，采用Docker容器化部署。核心功能为接收RabbitMQ消息，通过用户自定义JS脚本进行灵活处理，将处理结果"编织"到不同的目标系统中，包括MySQL、达梦数据库或HTTP接口等。

系统如其名，像编织工一样将来自不同消息队列的数据流，通过可编程的脚本逻辑，精准地编织到各种目标系统中，实现数据的智能路由和转换。sageWeaver（数据流编织器）详细设计文档

## 一、项目概述

本项目为“自定义消息转发后台服务”，以Go语言实现，采用Docker容器化部署。核心功能为接收RabbitMQ消息，通过用户自定义JS脚本处理后，结果经DataProxy转发至MySQL、达梦数据库或HTTP接口。

---

## 二、系统架构

- **开发语言**：Go (建议1.20+)
- **消息队列**：RabbitMQ
- **脚本引擎**：内嵌JavaScript引擎（推荐 [goja](https://github.com/dop251/goja)）
- **数据库**：支持MySQL、达梦（dm），可扩展
- **容器化**：Docker
- **配置管理**：环境变量 & 配置文件（YAML/JSON）
- **API接口**：RESTful（适配前端Vue3）

**架构示意**  
```
+--------------+        +---------------------+        +------------------+
|  RabbitMQ    |----->  |  MessageWeaver      |----->  | DataProxy (DB/HTTP)|
+--------------+        |  - MQ监听           |        +------------------+
                        |  - JS沙箱           |
                        |  - 数据代理         |
                        +---------------------+
                                   ^
                                   |
                         +---------------------+
                         |  前端管理(Vue3)     |
                         +---------------------+
```

---

## 三、核心数据模型

### 1. MessageHandler

负责处理MQ推送的消息，绑定多个MQ队列和多个DataProxy。

- `Name` (string): **全局唯一处理器名**
- `MqNames` ([]string): 该处理器关心的MQ队列名
- `Code` (string): JS处理脚本
- `Enabled` (bool): 启用状态
- `Description` (string)
- `DataProxyNames` ([]string): 处理结果需转发到的DataProxy名称
- `CreatedAt` (time.Time)
- `UpdatedAt` (time.Time)
- `Version` (int): 脚本版本
- `Author` (string)
- `Config` (map[string]interface{}): 扩展配置，如timeout、内存限制

**Go结构体示例**：
```go
type MessageHandler struct {
    Name           string                 `json:"name"`
    MqNames        []string               `json:"mqNames"`
    Code           string                 `json:"code"`
    Enabled        bool                   `json:"enabled"`
    Description    string                 `json:"description"`
    DataProxyNames []string               `json:"dataProxyNames"`
    CreatedAt      time.Time              `json:"createdAt"`
    UpdatedAt      time.Time              `json:"updatedAt"`
    Version        int                    `json:"version"`
    Author         string                 `json:"author"`
    Config         map[string]interface{} `json:"config"`
}
```

---

### 2. DataProxy（多态结构）

处理器执行完后，数据通过DataProxy进行落地或转发。支持三种类型，统一接口，类型字段区分。

#### 通用结构
```go
type DataProxy struct {
    Type        string                 `json:"type"`     // "mysql"|"dm"|"http"
    Name        string                 `json:"name"`     // 唯一
    Enabled     bool                   `json:"enabled"`
    Description string                 `json:"description"`
    CreatedAt   time.Time              `json:"createdAt"`
    UpdatedAt   time.Time              `json:"updatedAt"`
    Config      map[string]interface{} `json:"config"`    // 类型专属参数
}
```

#### MysqlDataProxy (Type="mysql")
- `Config["dsn"]`：MySQL连接串
- `Config["table"]`：目标表
- 其它连接池参数

#### DmDataProxy (Type="dm")
- `Config["dsn"]`：达梦连接串
- `Config["table"]`：目标表

#### HttpProxy (Type="http")
- `Config["url"]`：目标HTTP接口
- `Config["method"]`：HTTP方法
- `Config["headers"]`：map[string]string
- `Config["auth"]`：认证信息（如需要）
- `Config["timeout"]`：超时时间ms

---

## 四、主要流程与交互图

#### 1. 消息处理顺序

1. 客户端通过MQ客户端发送消息到RabbitMQ。
2. Go服务监听MQ，收到消息后，根据`mqNames`路由到一个或多个MessageHandler。
3. 调用goja沙箱执行JS脚本进行处理。
4. 处理结果根据配置，交由对应DataProxy（MySQL/达梦/HTTP）进行转发或落地。

#### 2. Mermaid序列图

```mermaid
sequenceDiagram
    participant Client as MQ客户端
    participant MQ as RabbitMQ
    participant MW as MessageWeaver
    participant JS as JS沙箱
    participant DP as DataProxy
    participant Target as 目标系统

    Client->>MQ: 发送标准格式消息
    Note right of Client: 包含messageId、eventType<br/>timestamp、data等字段
    
    MQ->>MW: 推送消息到队列
    Note right of MQ: 根据routing_key路由
    
    MW->>MW: 解析消息格式
    MW->>MW: 根据mqNames匹配Handler
    
    MW->>JS: 调用用户JS脚本
    Note right of JS: 沙箱执行，安全隔离<br/>支持超时和内存限制
    
    JS-->>MW: 返回处理结果
    
    MW->>DP: 转发处理后的数据
    Note right of DP: 根据DataProxyNames<br/>选择目标代理
    
    DP->>Target: 写入数据库或调用HTTP
    Note right of Target: MySQL/达梦/HTTP接口
    
    Target-->>DP: 响应结果
    DP-->>MW: 转发结果
    MW-->>MQ: ACK消息确认
    
    Note over Client,Target: 支持失败重试、死信队列<br/>完整的链路追踪和监控
```

---

### 3. MQ消息格式设计

#### 标准消息结构

为了确保MessageWeaver能够高效处理各种类型的消息，设计统一的消息格式规范：

```json
{
  "messageId": "uuid-string",
  "timestamp": "2025-07-12T10:30:00Z",
  "source": "system-name",
  "eventType": "user.login",
  "version": "1.0",
  "data": {
    // 业务数据（灵活结构）
  },
  "metadata": {
    "priority": "normal",
    "retryCount": 0,
    "maxRetries": 3,
    "tags": ["user", "authentication"],
    "correlationId": "trace-uuid"
  }
}
```

#### 字段说明

- **messageId** (string): 全局唯一消息标识符，用于幂等性控制和链路追踪
- **timestamp** (string): 消息产生时间，ISO 8601格式
- **source** (string): 消息来源系统标识
- **eventType** (string): 事件类型，用于分类和路由（如 `user.login`、`order.created`）
- **version** (string): 消息格式版本，支持向后兼容
- **data** (object): 核心业务数据，结构灵活，由具体业务定义
- **metadata** (object): 元数据信息
  - **priority** (string): 消息优先级 (`low`/`normal`/`high`/`critical`)
  - **retryCount** (int): 当前重试次数
  - **maxRetries** (int): 最大重试次数
  - **tags** ([]string): 消息标签，用于分类和过滤
  - **correlationId** (string): 关联ID，用于跟踪相关联的消息

#### 消息示例

**用户行为事件**：
```json
{
  "messageId": "123e4567-e89b-12d3-a456-************",
  "timestamp": "2025-07-12T10:30:00Z",
  "source": "user-service",
  "eventType": "user.action",
  "version": "1.0",
  "data": {
    "userId": "user123",
    "action": "view_product",
    "productId": "prod_456",
    "sessionId": "sess_abc123",
    "duration": 120,
    "deviceType": "mobile",
    "referrer": "search_engine"
  },
  "metadata": {
    "priority": "normal",
    "retryCount": 0,
    "maxRetries": 3,
    "tags": ["user", "behavior", "analytics"],
    "correlationId": "trace-456"
  }
}
```

**订单创建事件**：
```json
{
  "messageId": "223e4567-e89b-12d3-a456-426614174001",
  "timestamp": "2025-07-12T11:15:30Z",
  "source": "order-service",
  "eventType": "order.created",
  "version": "1.0",
  "data": {
    "orderId": "order_789",
    "customerId": "customer_456",
    "products": [
      {
        "productId": "prod_001",
        "quantity": 2,
        "price": 29.99
      }
    ],
    "totalAmount": 59.98,
    "currency": "USD",
    "orderStatus": "pending",
    "shippingAddress": {
      "street": "123 Main St",
      "city": "New York",
      "zipCode": "10001"
    }
  },
  "metadata": {
    "priority": "high",
    "retryCount": 0,
    "maxRetries": 5,
    "tags": ["order", "e-commerce", "payment"],
    "correlationId": "trace-789"
  }
}
```

**系统监控事件**：
```json
{
  "messageId": "323e4567-e89b-12d3-a456-426614174002",
  "timestamp": "2025-07-12T12:00:00Z",
  "source": "monitoring-service",
  "eventType": "system.alert",
  "version": "1.0",
  "data": {
    "alertType": "cpu_usage_high",
    "severity": "warning",
    "value": 85.5,
    "threshold": 80.0,
    "unit": "percentage",
    "hostname": "web-server-01",
    "details": {
      "duration": "5m",
      "trend": "increasing"
    }
  },
  "metadata": {
    "priority": "critical",
    "retryCount": 0,
    "maxRetries": 1,
    "tags": ["monitoring", "alert", "infrastructure"],
    "correlationId": "trace-monitor-001"
  }
}
```

#### 轻量级消息格式（可选）

对于高频、简单的消息，可以使用简化格式：

```json
{
  "id": "short-uuid",
  "type": "heartbeat",
  "data": {
    "status": "ok",
    "timestamp": "2025-07-12T12:00:00Z"
  }
}
```

#### RabbitMQ消息属性

除了消息体之外，还应该合理设置RabbitMQ的消息属性：

- **routing_key**: 根据eventType设置，如 `user.login`、`order.created`
- **headers**: 包含source、priority等关键字段，便于快速过滤
- **delivery_mode**: 设置为2（持久化），确保消息不丢失
- **expiration**: 根据业务设置消息过期时间
- **message_id**: 与消息体中的messageId保持一致

**Go发送示例**：
```go
publishing := amqp.Publishing{
    Headers: amqp.Table{
        "source":    "order-service",
        "eventType": "order.created",
        "priority":  "high",
    },
    MessageId:    message.MessageId,
    Timestamp:    time.Now(),
    ContentType:  "application/json",
    DeliveryMode: amqp.Persistent,
    Body:         messageBody,
}

err := channel.Publish(
    "events.exchange", // exchange
    "order.created",   // routing key
    false,             // mandatory
    false,             // immediate
    publishing,
)
```

---

#### MQ队列设计建议

**队列命名规范**：
- 格式：`{service}.{eventType}.{env}`
- 示例：`order.created.prod`、`inventory.updated.dev`

**Exchange设计**：
- **Topic Exchange**: `events.exchange` - 用于业务事件路由
- **Direct Exchange**: `system.exchange` - 用于系统内部通信
- **Dead Letter Exchange**: `dlx.exchange` - 处理失败消息

**队列配置建议**：
```json
{
  "durable": true,
  "autoDelete": false,
  "arguments": {
    "x-message-ttl": 3600000,
    "x-max-length": 10000,
    "x-dead-letter-exchange": "dlx.exchange",
    "x-dead-letter-routing-key": "failed"
  }
}
```

**消息发送最佳实践**：

1. **幂等性保证**：使用messageId确保消息不重复处理
2. **消息确认**：启用publisher confirms确保消息投递成功
3. **重试机制**：设置合理的重试次数和退避策略
4. **监控告警**：监控队列长度、消费速率、错误率等指标

**Go客户端发送示例**：
```go
type MQClient struct {
    conn    *amqp.Connection
    channel *amqp.Channel
}

func (c *MQClient) SendMessage(msg *StandardMessage) error {
    // 序列化消息
    body, err := json.Marshal(msg)
    if err != nil {
        return err
    }
    
    // 设置消息属性
    publishing := amqp.Publishing{
        Headers: amqp.Table{
            "source":       msg.Source,
            "eventType":    msg.EventType,
            "priority":     msg.Metadata.Priority,
            "correlationId": msg.Metadata.CorrelationId,
        },
        MessageId:    msg.MessageId,
        Timestamp:    time.Now(),
        ContentType:  "application/json",
        DeliveryMode: amqp.Persistent,
        Body:         body,
    }
    
    // 发送消息
    return c.channel.Publish(
        "events.exchange",    // exchange
        msg.EventType,        // routing key
        false,               // mandatory
        false,               // immediate
        publishing,
    )
}

// 标准消息结构
type StandardMessage struct {
    MessageId string                 `json:"messageId"`
    Timestamp string                 `json:"timestamp"`
    Source    string                 `json:"source"`
    EventType string                 `json:"eventType"`
    Version   string                 `json:"version"`
    Data      map[string]interface{} `json:"data"`
    Metadata  MessageMetadata        `json:"metadata"`
}

type MessageMetadata struct {
    Priority      string   `json:"priority"`
    RetryCount    int      `json:"retryCount"`
    MaxRetries    int      `json:"maxRetries"`
    Tags          []string `json:"tags"`
    CorrelationId string   `json:"correlationId"`
}
```

---

## 五、管理API设计（RESTful）

### MessageHandler管理

- `GET /api/message-handlers` 查询全部
- `POST /api/message-handlers` 新建
- `PUT /api/message-handlers/:name` 更新
- `DELETE /api/message-handlers/:name` 删除

### DataProxy管理

- `GET /api/data-proxies` 查询全部
- `POST /api/data-proxies` 新建
- `PUT /api/data-proxies/:name` 更新
- `DELETE /api/data-proxies/:name` 删除

### 日志与监控

- `GET /api/logs` 查询处理日志
- `GET /api/health` 健康检查

### 脚本测试与调试

- `POST /api/message-handlers/:name/test` 测试JS脚本执行
- `POST /api/message-handlers/:name/debug` 调试模式执行（返回详细日志）
- `GET /api/message-handlers/:name/syntax-check` 语法检查

### 系统监控

- `GET /api/metrics` 系统指标（消息处理量、成功率、延迟等）
- `GET /api/queues/status` MQ队列状态
- `GET /api/data-proxies/status` DataProxy连接状态

---

## 六、API接口详细定义

#### MessageHandler CRUD

**创建处理器**
```http
POST /api/message-handlers
Content-Type: application/json

{
  "name": "order-analytics-handler",
  "mqNames": ["order.created.prod"],
  "code": "function process(message) { return { orderId: message.data.orderId, totalAmount: message.data.totalAmount, timestamp: new Date().toISOString() }; }",
  "enabled": true,
  "description": "处理订单创建事件进行数据分析",
  "dataProxyNames": ["mysql-analytics-db", "analytics-api"],
  "author": "developer",
  "config": {
    "timeout": 5000,
    "memoryLimit": "64MB"
  }
}
```

**响应示例**
```http
HTTP/1.1 201 Created
Content-Type: application/json

{
  "success": true,  "data": {
    "name": "order-analytics-handler",
    "version": 1,
    "createdAt": "2025-07-12T10:30:00Z"
  }
}
```

**查询处理器**
```http
GET /api/message-handlers?page=1&limit=10&enabled=true

Response:
{
  "success": true,
  "data": {
    "handlers": [...],
    "total": 25,
    "page": 1,
    "limit": 10
  }
}
```

#### DataProxy CRUD

**创建MySQL代理**
```http
POST /api/data-proxies
Content-Type: application/json

{
  "type": "mysql",
  "name": "mysql-analytics-db",
  "enabled": true,
  "description": "数据分析数据库",
  "config": {
    "dsn": "user:password@tcp(localhost:3306)/analyticsdb?charset=utf8mb4",
    "table": "analytics_events",
    "maxOpenConns": 10,
    "maxIdleConns": 5,
    "connMaxLifetime": 3600
  }
}
```

**创建HTTP代理**
```http
POST /api/data-proxies
Content-Type: application/json

{
  "type": "http",
  "name": "analytics-api",
  "enabled": true,
  "description": "数据分析API",
  "config": {
    "url": "https://analytics.example.com/api/events",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "Authorization": "Bearer ${API_TOKEN}"
    },
    "timeout": 10000,
    "retries": 3
  }
}
```

#### 脚本测试接口

**测试脚本执行**
```http
POST /api/message-handlers/order-analytics-handler/test
Content-Type: application/json

{
  "message": {
    "messageId": "test-123",
    "timestamp": "2025-07-12T10:30:00Z",
    "source": "order-service",
    "eventType": "order.created",
    "version": "1.0",
    "data": {
      "orderId": "order_789",
      "totalAmount": 99.99
    }
  }
}
```

**响应示例**
```http
{
  "success": true,  "data": {
    "result": {
      "orderId": "order_789",
      "totalAmount": 99.99,
      "timestamp": "2025-07-12T10:30:15Z"
    },
    "executionTime": 25,
    "memoryUsed": "2.1MB"
  }
}
```

---

### JS脚本运行环境详细设计

#### 脚本接口规范

**标准处理函数**
```javascript
/**
 * 消息处理函数（必须实现）
 * @param {Object} message - 标准消息对象
 * @param {Object} context - 运行时上下文
 * @returns {Object|Array|null} 处理结果
 */
function process(message, context) {
    // 获取消息数据
    const { messageId, timestamp, source, eventType, data, metadata } = message;
    
    // 访问上下文信息
    const { logger, utils, config } = context;
      // 处理逻辑
    if (eventType === 'order.created') {
        logger.info('Processing order created event', { orderId: data.orderId });
        
        return {
            orderId: data.orderId,
            totalAmount: data.totalAmount,
            processedAt: utils.now(),
            source: source
        };
    }
    
    // 返回null表示跳过处理
    return null;
}

/**
 * 初始化函数（可选）
 */
function init(config) {
    // 脚本初始化逻辑
    console.log('Handler initialized with config:', config);
}

/**
 * 清理函数（可选）
 */
function cleanup() {
    // 清理资源
    console.log('Handler cleanup');
}
```

#### 运行时上下文API

**Logger对象**
```javascript
context.logger = {
    debug(message, data),
    info(message, data),
    warn(message, data),
    error(message, data)
}
```

**Utils工具库**
```javascript
context.utils = {
    // 时间工具
    now(): string,              // 当前ISO时间
    timestamp(): number,        // Unix时间戳
    parseTime(str): Date,       // 解析时间字符串
    
    // 数据工具
    deepClone(obj): Object,     // 深度克隆
    merge(obj1, obj2): Object,  // 对象合并
    pick(obj, keys): Object,    // 选择字段
    
    // 验证工具
    isEmail(str): boolean,      // 邮箱验证
    isPhone(str): boolean,      // 手机号验证
    isUrl(str): boolean,        // URL验证
    
    // 加密工具
    md5(str): string,           // MD5哈希
    sha256(str): string,        // SHA256哈希
    base64Encode(str): string,  // Base64编码
    base64Decode(str): string,  // Base64解码
    
    // HTTP工具
    httpGet(url, headers): Promise<Object>,
    httpPost(url, data, headers): Promise<Object>
}
```

**配置访问**
```javascript
// 访问处理器配置
const timeout = context.config.timeout || 5000;
const customSetting = context.config.customSetting;

// 访问全局配置
const dbConfig = context.globalConfig.database;
```

#### 脚本示例

**用户行为分析处理**
```javascript
function process(message, context) {
    const { data } = message;
    const { logger, utils } = context;
    
    // 验证必要字段
    if (!data.userId || !data.action) {
        logger.warn('Missing required fields', { messageId: message.messageId });
        return null;
    }
    
    // 处理用户行为数据
    const result = {
        user_id: data.userId,
        action: data.action,
        product_id: data.productId,
        session_id: data.sessionId,
        duration: data.duration,
        device_type: data.deviceType,
        referrer: data.referrer,
        processed_at: utils.now(),
        message_id: message.messageId
    };
    
    // 添加行为分类（示例）
    if (data.action === 'view_product') {
        result.category = 'product_interaction';
        result.priority = 'normal';
    } else if (data.action === 'purchase') {
        result.category = 'conversion';
        result.priority = 'high';
    }
    
    logger.info('User behavior processed', { 
        userId: data.userId, 
        action: data.action 
    });
    
    return result;
}
```

**订单事件聚合处理**
```javascript
function process(message, context) {
    const { data } = message;
    const { logger, utils } = context;
    
    if (message.eventType === 'order.created') {
        // 计算订单统计
        const orderSummary = {
            order_id: data.orderId,
            customer_id: data.customerId,
            total_amount: data.totalAmount,
            product_count: data.products.length,
            created_at: message.timestamp,
            processed_at: utils.now()
        };
        
        // 生成产品明细
        const productDetails = data.products.map(product => ({
            order_id: data.orderId,
            product_id: product.productId,
            quantity: product.quantity,
            price: product.price,
            total: product.quantity * product.price
        }));
        
        return {
            order_summary: orderSummary,
            product_details: productDetails
        };
    }
    
    return null;
}
```

**数据转换和过滤**
```javascript
function process(message, context) {
    const { data, metadata } = message;
    const { logger, utils } = context;
    
    // 基于标签过滤
    if (!metadata.tags.includes('important')) {
        return null;
    }
    
    // 数据清洗和转换
    const cleaned = utils.pick(data, ['userId', 'action', 'timestamp', 'details']);
    
    // 敏感信息脱敏
    if (cleaned.details && cleaned.details.email) {
        cleaned.details.email = maskEmail(cleaned.details.email);
    }
    
    return cleaned;
}

function maskEmail(email) {
    const parts = email.split('@');
    if (parts.length !== 2) return email;
    
    const username = parts[0];
    const domain = parts[1];
    
    if (username.length <= 2) return email;
    
    const masked = username[0] + '*'.repeat(username.length - 2) + username[username.length - 1];
    return masked + '@' + domain;
}
```

#### 安全限制

**沙箱限制**
- 禁止访问文件系统
- 禁止网络访问（除了context.utils提供的HTTP方法）
- 禁止执行系统命令
- 限制内存使用（默认64MB）
- 限制执行时间（默认5秒）
- 禁止无限循环和递归

**代码检查**
- 语法检查
- 禁用危险函数（eval、Function等）
- 代码复杂度检查
- 性能检查（循环次数限制等）

---

### 八、数据库设计

#### 核心表结构

**message_handlers 表**
```sql
CREATE TABLE message_handlers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL UNIQUE COMMENT '处理器名称',
    mq_names JSON NOT NULL COMMENT 'MQ队列名称列表',
    code LONGTEXT NOT NULL COMMENT 'JS处理脚本',
    enabled BOOLEAN DEFAULT true COMMENT '启用状态',
    description TEXT COMMENT '描述',
    data_proxy_names JSON NOT NULL COMMENT 'DataProxy名称列表',
    version INT DEFAULT 1 COMMENT '脚本版本',
    author VARCHAR(255) COMMENT '作者',
    config JSON COMMENT '扩展配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_enabled (enabled),
    INDEX idx_author (author),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息处理器配置表';
```

**data_proxies 表**
```sql
CREATE TABLE data_proxies (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    type ENUM('mysql', 'dm', 'http') NOT NULL COMMENT '代理类型',
    name VARCHAR(255) NOT NULL UNIQUE COMMENT '代理名称',
    enabled BOOLEAN DEFAULT true COMMENT '启用状态',
    description TEXT COMMENT '描述',
    config JSON NOT NULL COMMENT '配置信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_type (type),
    INDEX idx_enabled (enabled),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据代理配置表';
```

**message_logs 表**
```sql
CREATE TABLE message_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(255) NOT NULL COMMENT '消息ID',
    handler_name VARCHAR(255) NOT NULL COMMENT '处理器名称',
    mq_name VARCHAR(255) NOT NULL COMMENT 'MQ队列名',
    event_type VARCHAR(255) NOT NULL COMMENT '事件类型',
    source VARCHAR(255) NOT NULL COMMENT '消息源',
    status ENUM('processing', 'success', 'failed', 'retry') DEFAULT 'processing' COMMENT '处理状态',
    execution_time INT COMMENT '执行时间(ms)',
    memory_used VARCHAR(20) COMMENT '内存使用',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    correlation_id VARCHAR(255) COMMENT '关联ID',
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_message_id (message_id),
    INDEX idx_handler_name (handler_name),
    INDEX idx_status (status),
    INDEX idx_event_type (event_type),
    INDEX idx_correlation_id (correlation_id),
    INDEX idx_processed_at (processed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息处理日志表';
```

**system_metrics 表**
```sql
CREATE TABLE system_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(255) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(20,6) NOT NULL COMMENT '指标值',
    tags JSON COMMENT '标签',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    INDEX idx_metric_name (metric_name),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统指标表';
```

#### 初始化SQL

**init.sql**
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS messageweaver 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE messageweaver;

-- 创建表（省略，见上面的表结构）

-- 插入示例数据
INSERT INTO message_handlers (name, mq_names, code, enabled, description, data_proxy_names, author, config) VALUES
('order-analytics-handler', 
 '["order.created.prod"]', 
 'function process(message, context) { return { orderId: message.data.orderId, totalAmount: message.data.totalAmount }; }',
 true,
 '订单创建事件分析处理器',
 '["mysql-analytics-db"]',
 'system',
 '{"timeout": 5000, "memoryLimit": "64MB"}'
);

INSERT INTO data_proxies (type, name, enabled, description, config) VALUES
('mysql', 
 'mysql-analytics-db',
 true,
 '数据分析数据库代理',
 '{"dsn": "user:password@tcp(localhost:3306)/analyticsdb", "table": "order_events"}'
),
('http',
 'analytics-api',
 true,
 '数据分析API代理',
 '{"url": "https://api.example.com/events", "method": "POST", "timeout": 10000}'
);
```

---

### 九、部署与运维

#### 部署架构

**生产环境推荐架构**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │───▶│  MessageWeaver  │───▶│    Database     │
│    (Nginx)      │    │   Instances     │    │   (MySQL)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   RabbitMQ      │
                       │   Cluster       │
                       └─────────────────┘
```

#### Kubernetes部署

**deployment.yaml**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: messageweaver
  labels:
    app: messageweaver
spec:
  replicas: 3
  selector:
    matchLabels:
      app: messageweaver
  template:
    metadata:
      labels:
        app: messageweaver
    spec:
      containers:
      - name: messageweaver
        image: messageweaver:latest
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_DSN
          valueFrom:
            secretKeyRef:
              name: messageweaver-secret
              key: database-dsn
        - name: RABBITMQ_URL
          valueFrom:
            secretKeyRef:
              name: messageweaver-secret
              key: rabbitmq-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: messageweaver-secret
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: messageweaver-service
spec:
  selector:
    app: messageweaver
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
```

---

## 十、测试策略

#### 单元测试

**Handler测试示例**
```go
// handler_test.go
func TestMessageHandler_Process(t *testing.T) {
    handler := &MessageHandler{
        Name: "test-handler",
        Code: `function process(message, context) { 
            return { userId: message.data.userId, processed: true }; 
        }`,
    }
    
    message := &StandardMessage{
        MessageId: "test-123",
        EventType: "user.login",
        Data: map[string]interface{}{
            "userId": "user123",
        },
    }
    
    result, err := handler.Execute(message)
    assert.NoError(t, err)
    assert.Equal(t, "user123", result["userId"])
    assert.True(t, result["processed"].(bool))
}
```

**DataProxy测试示例**
```go
// dataproxy_test.go
func TestMySQLDataProxy_Send(t *testing.T) {
    // 使用testcontainers创建测试数据库
    ctx := context.Background()
    mysqlContainer, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
        ContainerRequest: testcontainers.ContainerRequest{
            Image:        "mysql:8.0",
            ExposedPorts: []string{"3306/tcp"},
            Env: map[string]string{
                "MYSQL_ROOT_PASSWORD": "password",
                "MYSQL_DATABASE":      "testdb",
            },
        },
        Started: true,
    })
    defer mysqlContainer.Terminate(ctx)
    
    // 测试数据写入
    proxy := &MySQLDataProxy{
        Config: MySQLConfig{
            DSN:   "root:password@tcp(localhost:3306)/testdb",
            Table: "test_events",
        },
    }
    
    data := map[string]interface{}{
        "user_id":    "user123",
        "event_type": "login",
        "timestamp":  time.Now(),
    }
    
    err = proxy.Send(data)
    assert.NoError(t, err)
}
```

#### 集成测试

**端到端测试**
```go
// e2e_test.go
func TestE2E_MessageProcessing(t *testing.T) {
    // 启动测试环境
    testEnv := setupTestEnvironment(t)
    defer testEnv.Cleanup()
    
    // 1. 创建处理器
    handler := &MessageHandler{
        Name:    "e2e-test-handler",
        MqNames: []string{"test.queue"},
        Code:    `function process(message) { return message.data; }`,
        DataProxyNames: []string{"test-proxy"},
    }
    
    err := testEnv.HandlerService.Create(handler)
    assert.NoError(t, err)
    
    // 2. 创建DataProxy
    proxy := &DataProxy{
        Type: "http",
        Name: "test-proxy",
        Config: map[string]interface{}{
            "url":    testEnv.MockServer.URL + "/webhook",
            "method": "POST",
        },
    }
    
    err = testEnv.ProxyService.Create(proxy)
    assert.NoError(t, err)
    
    // 3. 发送测试消息
    message := &StandardMessage{
        MessageId: "e2e-test-123",
        EventType: "test.event",
        Data: map[string]interface{}{
            "testField": "testValue",
        },
    }
    
    err = testEnv.MQClient.Publish("test.queue", message)
    assert.NoError(t, err)
    
    // 4. 验证处理结果
    testEnv.MockServer.WaitForRequests(1, 5*time.Second)
    receivedData := testEnv.MockServer.GetLastRequest()
    assert.Equal(t, "testValue", receivedData["testField"])
}
```

#### 性能测试

**负载测试脚本**
```go
// load_test.go
func TestLoadTesting(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping load test in short mode")
    }
    
    const (
        concurrency = 100
        duration    = 30 * time.Second
        messageRate = 1000 // messages per second
    )
    
    // 创建测试处理器
    setupTestHandler(t)
    
    // 启动负载测试
    ctx, cancel := context.WithTimeout(context.Background(), duration)
    defer cancel()
    
    var wg sync.WaitGroup
    results := make(chan TestResult, 1000)
    
    // 启动多个goroutine发送消息
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            sendMessages(ctx, messageRate/concurrency, results)
        }()
    }
    
    // 收集结果
    go func() {
        wg.Wait()
        close(results)
    }()
    
    // 分析性能指标
    stats := analyzeResults(results)
    
    // 断言性能要求
    assert.Less(t, stats.AvgLatency, 100*time.Millisecond)
    assert.Greater(t, stats.SuccessRate, 0.99)
    assert.Less(t, stats.P95Latency, 200*time.Millisecond)
}
```

---

## 十一、错误处理与恢复

#### 错误分类

**系统级错误**
- 数据库连接失败
- MQ连接断开
- 内存不足
- 网络超时

**业务级错误**
- 消息格式错误
- JS脚本执行失败
- 数据验证失败
- 目标系统不可用

**脚本级错误**
- 语法错误
- 运行时异常
- 超时错误
- 内存超限

#### 错误处理策略

**重试机制**
```go
type RetryConfig struct {
    MaxRetries    int           `json:"maxRetries"`
    InitialDelay  time.Duration `json:"initialDelay"`
    MaxDelay      time.Duration `json:"maxDelay"`
    BackoffFactor float64       `json:"backoffFactor"`
}

func (h *MessageHandler) ProcessWithRetry(message *StandardMessage) error {
    config := h.GetRetryConfig()
    
    for attempt := 0; attempt <= config.MaxRetries; attempt++ {
        err := h.Process(message)
        if err == nil {
            return nil
        }
        
        // 判断是否需要重试
        if !isRetryableError(err) {
            return err
        }
        
        if attempt < config.MaxRetries {
            delay := calculateDelay(attempt, config)
            time.Sleep(delay)
        }
    }
    
    return fmt.Errorf("max retries exceeded")
}

func calculateDelay(attempt int, config RetryConfig) time.Duration {
    delay := config.InitialDelay * time.Duration(math.Pow(config.BackoffFactor, float64(attempt)))
    if delay > config.MaxDelay {
        delay = config.MaxDelay
    }
    return delay
}
```

**断路器模式**
```go
type CircuitBreaker struct {
    maxFailures  int
    resetTimeout time.Duration
    failures     int
    lastFailTime time.Time
    state        CircuitState
    mutex        sync.RWMutex
}

type CircuitState int

const (
    StateClosed CircuitState = iota
    StateOpen
    StateHalfOpen
)

func (cb *CircuitBreaker) Execute(fn func() error) error {
    cb.mutex.Lock()
    defer cb.mutex.Unlock()
    
    switch cb.state {
    case StateOpen:
        if time.Since(cb.lastFailTime) > cb.resetTimeout {
            cb.state = StateHalfOpen
            cb.failures = 0
        } else {
            return errors.New("circuit breaker is open")
        }
    case StateHalfOpen:
        // 允许一次尝试
    }
    
    err := fn()
    if err != nil {
        cb.failures++
        cb.lastFailTime = time.Now()
        
        if cb.failures >= cb.maxFailures {
            cb.state = StateOpen
        }
        return err
    }
    
    cb.failures = 0
    cb.state = StateClosed
    return nil
}
```

**死信队列处理**
```go
type DeadLetterHandler struct {
    dlxExchange string
    dlxQueue    string
    channel     *amqp.Channel
}

func (dlh *DeadLetterHandler) HandleFailedMessage(
    message *StandardMessage, 
    error error,
    retryCount int,
) error {
    // 创建死信消息
    dlMessage := DeadLetterMessage{
        OriginalMessage: message,
        Error:          error.Error(),
        RetryCount:     retryCount,
        FailedAt:       time.Now(),
        Reason:         classifyError(error),
    }
    
    body, err := json.Marshal(dlMessage)
    if err != nil {
        return err
    }
    
    // 发送到死信队列
    return dlh.channel.Publish(
        dlh.dlxExchange,
        dlh.dlxQueue,
        false,
        false,
        amqp.Publishing{
            ContentType:  "application/json",
            Body:        body,
            Timestamp:   time.Now(),
            Headers: amqp.Table{
                "x-original-message-id": message.MessageId,
                "x-failure-reason":      dlMessage.Reason,
                "x-retry-count":        retryCount,
            },
        },
    )
}
```

#### 系统恢复

**自动恢复机制**
```go
type RecoveryManager struct {
    healthCheckers []HealthChecker
    recoveryActions []RecoveryAction
    checkInterval  time.Duration
}

func (rm *RecoveryManager) Start(ctx context.Context) {
    ticker := time.NewTicker(rm.checkInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            rm.performHealthCheck()
        }
    }
}

func (rm *RecoveryManager) performHealthCheck() {
    for _, checker := range rm.healthCheckers {
        if !checker.IsHealthy() {
            log.Warn("Component unhealthy", 
                zap.String("component", checker.Name()),
                zap.Error(checker.LastError()),
            )
            
            // 执行恢复动作
            for _, action := range rm.recoveryActions {
                if action.CanRecover(checker) {
                    err := action.Recover(checker)
                    if err != nil {
                        log.Error("Recovery failed",
                            zap.String("component", checker.Name()),
                            zap.String("action", action.Name()),
                            zap.Error(err),
                        )
                    } else {
                        log.Info("Recovery successful",
                            zap.String("component", checker.Name()),
                            zap.String("action", action.Name()),
                        )
                    }
                }
            }
        }
    }
}
```

**优雅关闭**
```go
func (s *Server) GracefulShutdown(ctx context.Context) error {
    log.Info("Starting graceful shutdown...")
    
    // 1. 停止接收新请求
    s.httpServer.SetKeepAlivesEnabled(false)
    
    // 2. 停止消费新消息
    s.mqConsumer.Stop()
    
    // 3. 等待正在处理的消息完成
    done := make(chan struct{})
    go func() {
        s.waitGroup.Wait()
        close(done)
    }()
    
    select {
    case <-done:
        log.Info("All messages processed")
    case <-ctx.Done():
        log.Warn("Shutdown timeout, forcing exit")
    }
    
    // 4. 关闭连接
    if err := s.mqConnection.Close(); err != nil {
        log.Error("Error closing MQ connection", zap.Error(err))
    }
    
    if err := s.db.Close(); err != nil {
        log.Error("Error closing database", zap.Error(err))
    }
    
    // 5. 关闭HTTP服务器
    shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    if err := s.httpServer.Shutdown(shutdownCtx); err != nil {
        log.Error("Error shutting down HTTP server", zap.Error(err))
        return err
    }
    
    log.Info("Graceful shutdown completed")
    return nil
}
```

---