package mq

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"messageweaver/internal/config"
	"messageweaver/internal/handler"
	"messageweaver/internal/logger"
	"messageweaver/internal/models"
	"messageweaver/pkg/types"

	"github.com/streadway/amqp"
	"go.uber.org/zap"
)

// Consumer 消息消费者
type Consumer struct {
	config         config.RabbitMQConfig
	logger         logger.Logger
	connection     *Connection
	handlerManager *handler.Manager
	consumers      map[string]*QueueConsumer
	mutex          sync.RWMutex
	running        bool
	wg             sync.WaitGroup
}

// QueueConsumer 队列消费者
type QueueConsumer struct {
	queueName   string
	consumerTag string
	deliveries  <-chan amqp.Delivery
	done        chan bool
}

// NewConsumer 创建消息消费者
func NewConsumer(cfg config.RabbitMQConfig, logger logger.Logger, handlerManager *handler.Manager) (*Consumer, error) {
	conn, err := NewConnection(cfg, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}

	return &Consumer{
		config:         cfg,
		logger:         logger,
		connection:     conn,
		handlerManager: handlerManager,
		consumers:      make(map[string]*QueueConsumer),
	}, nil
}

// Start 启动消费者
func (c *Consumer) Start(ctx context.Context) error {
	c.mutex.Lock()
	if c.running {
		c.mutex.Unlock()
		return fmt.Errorf("consumer is already running")
	}
	c.running = true
	c.mutex.Unlock()

	c.logger.Info("Starting message consumer")

	// 声明交换机
	if err := c.setupExchanges(); err != nil {
		return fmt.Errorf("failed to setup exchanges: %w", err)
	}

	// 启动队列消费者
	if err := c.startQueueConsumers(ctx); err != nil {
		return fmt.Errorf("failed to start queue consumers: %w", err)
	}

	// 等待上下文取消
	<-ctx.Done()

	c.logger.Info("Stopping message consumer")
	return c.Stop()
}

// Stop 停止消费者
func (c *Consumer) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.running {
		return nil
	}

	c.running = false

	// 停止所有队列消费者
	for _, consumer := range c.consumers {
		close(consumer.done)
	}

	// 等待所有goroutine结束
	c.wg.Wait()

	// 关闭连接
	if err := c.connection.Close(); err != nil {
		c.logger.Error("Failed to close connection", zap.Error(err))
		return err
	}

	c.logger.Info("Message consumer stopped")
	return nil
}

// setupExchanges 设置交换机
func (c *Consumer) setupExchanges() error {
	// 声明主交换机
	if err := c.connection.DeclareExchange(
		c.config.Exchange,
		c.config.ExchangeType,
		true,  // durable
		false, // auto-delete
	); err != nil {
		return fmt.Errorf("failed to declare exchange %s: %w", c.config.Exchange, err)
	}

	// 声明死信交换机
	if err := c.connection.DeclareExchange(
		c.config.DeadLetterExchange,
		"direct",
		true,  // durable
		false, // auto-delete
	); err != nil {
		return fmt.Errorf("failed to declare dead letter exchange %s: %w", c.config.DeadLetterExchange, err)
	}

	return nil
}

// startQueueConsumers 启动队列消费者
func (c *Consumer) startQueueConsumers(ctx context.Context) error {
	// 获取所有已加载的处理器
	handlers := c.handlerManager.GetLoadedHandlers()

	// 收集所有需要监听的队列
	queueSet := make(map[string]bool)
	for _, handler := range handlers {
		if handler.Enabled {
			for _, mqName := range handler.MqNames {
				queueSet[mqName] = true
			}
		}
	}

	// 为每个队列启动消费者
	for queueName := range queueSet {
		if err := c.startQueueConsumer(ctx, queueName); err != nil {
			return fmt.Errorf("failed to start consumer for queue %s: %w", queueName, err)
		}
	}

	return nil
}

// startQueueConsumer 启动单个队列消费者
func (c *Consumer) startQueueConsumer(ctx context.Context, queueName string) error {
	// 声明队列
	args := amqp.Table{
		"x-dead-letter-exchange":    c.config.DeadLetterExchange,
		"x-dead-letter-routing-key": "failed",
		"x-message-ttl":             3600000, // 1小时TTL
	}

	queue, err := c.connection.DeclareQueue(queueName, true, false, args)
	if err != nil {
		return fmt.Errorf("failed to declare queue: %w", err)
	}

	// 绑定队列到交换机
	if err := c.connection.BindQueue(queue.Name, queueName, c.config.Exchange); err != nil {
		return fmt.Errorf("failed to bind queue: %w", err)
	}

	// 开始消费
	consumerTag := fmt.Sprintf("consumer-%s-%d", queueName, time.Now().Unix())
	deliveries, err := c.connection.Consume(queue.Name, consumerTag)
	if err != nil {
		return fmt.Errorf("failed to start consuming: %w", err)
	}

	// 创建队列消费者
	queueConsumer := &QueueConsumer{
		queueName:   queueName,
		consumerTag: consumerTag,
		deliveries:  deliveries,
		done:        make(chan bool),
	}

	c.mutex.Lock()
	c.consumers[queueName] = queueConsumer
	c.mutex.Unlock()

	// 启动消息处理goroutine
	c.wg.Add(1)
	go c.handleMessages(ctx, queueConsumer)

	c.logger.Info("Started queue consumer",
		zap.String("queue", queueName),
		zap.String("consumer_tag", consumerTag),
	)

	return nil
}

// handleMessages 处理消息
func (c *Consumer) handleMessages(ctx context.Context, queueConsumer *QueueConsumer) {
	defer c.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-queueConsumer.done:
			return
		case delivery, ok := <-queueConsumer.deliveries:
			if !ok {
				c.logger.Warn("Delivery channel closed", zap.String("queue", queueConsumer.queueName))
				return
			}

			c.processMessage(delivery, queueConsumer.queueName)
		}
	}
}

// processMessage 处理单个消息
func (c *Consumer) processMessage(delivery amqp.Delivery, queueName string) {
	startTime := time.Now()

	// 解析消息
	var message types.StandardMessage
	if err := json.Unmarshal(delivery.Body, &message); err != nil {
		c.logger.Error("Failed to parse message",
			zap.String("queue", queueName),
			zap.Error(err),
			zap.ByteString("body", delivery.Body),
		)
		delivery.Nack(false, false) // 拒绝消息，不重新入队
		return
	}

	c.logger.Debug("Processing message",
		zap.String("queue", queueName),
		zap.String("message_id", message.MessageId),
		zap.String("event_type", message.EventType),
	)

	// 获取处理该队列的处理器
	handlers := c.handlerManager.GetHandlersForMQ(queueName)
	if len(handlers) == 0 {
		c.logger.Warn("No handlers found for queue", zap.String("queue", queueName))
		delivery.Ack(false) // 确认消息，避免重复处理
		return
	}

	// 处理消息
	success := true
	for _, handler := range handlers {
		if err := c.processWithHandler(handler, &message); err != nil {
			c.logger.Error("Handler processing failed",
				zap.String("handler", handler.Name),
				zap.String("message_id", message.MessageId),
				zap.Error(err),
			)
			success = false
		}
	}

	// 确认或拒绝消息
	if success {
		delivery.Ack(false)
		c.logger.Debug("Message processed successfully",
			zap.String("queue", queueName),
			zap.String("message_id", message.MessageId),
			zap.Duration("duration", time.Since(startTime)),
		)
	} else {
		// 检查是否应该重试
		if message.ShouldRetry() {
			message.IncrementRetry()
			c.requeueMessage(&message, delivery)
		} else {
			delivery.Nack(false, false) // 发送到死信队列
			c.logger.Error("Message processing failed, sent to DLQ",
				zap.String("queue", queueName),
				zap.String("message_id", message.MessageId),
			)
		}
	}
}

// processWithHandler 使用指定处理器处理消息
func (c *Consumer) processWithHandler(handler *models.MessageHandler, message *types.StandardMessage) error {
	result, err := c.handlerManager.ProcessMessage(handler, message)
	if err != nil {
		return err
	}

	if !result.Success {
		return fmt.Errorf("processing failed: %s", result.Error)
	}

	return nil
}

// requeueMessage 重新入队消息
func (c *Consumer) requeueMessage(message *types.StandardMessage, delivery amqp.Delivery) {
	// 更新消息内容
	body, err := json.Marshal(message)
	if err != nil {
		c.logger.Error("Failed to marshal message for requeue", zap.Error(err))
		delivery.Nack(false, false)
		return
	}

	// 延迟重新发布
	go func() {
		time.Sleep(time.Duration(message.Metadata.RetryCount) * time.Second)

		err := c.connection.Publish(c.config.Exchange, delivery.RoutingKey, amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent,
			Timestamp:    time.Now(),
		})

		if err != nil {
			c.logger.Error("Failed to requeue message", zap.Error(err))
		}
	}()

	delivery.Ack(false)
}

// GetStatus 获取消费者状态
func (c *Consumer) GetStatus() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	status := map[string]interface{}{
		"running":    c.running,
		"connected":  c.connection.IsConnected(),
		"consumers":  len(c.consumers),
		"connection": c.connection.GetConnectionInfo(),
	}

	queues := make(map[string]interface{})
	for queueName, consumer := range c.consumers {
		queues[queueName] = map[string]interface{}{
			"consumer_tag": consumer.consumerTag,
		}
	}
	status["queues"] = queues

	return status
}
