package models

import (
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// DataProxyType 数据代理类型
type DataProxyType string

const (
	ProxyTypeMySQL DataProxyType = "mysql"
	ProxyTypeDM    DataProxyType = "dm"
	ProxyTypeHTTP  DataProxyType = "http"
)

// DataProxy 数据代理模型
type DataProxy struct {
	ID          uint                   `json:"id" gorm:"primaryKey"`
	Type        DataProxyType          `json:"type" gorm:"not null" validate:"required,oneof=mysql dm http"`
	Name        string                 `json:"name" gorm:"uniqueIndex;not null" validate:"required,min=1,max=255"`
	Enabled     bool                   `json:"enabled" gorm:"default:true"`
	Description string                 `json:"description" gorm:"type:text"`
	Config      map[string]interface{} `json:"config" gorm:"type:json;not null" validate:"required"`
	CreatedAt   time.Time              `json:"createdAt"`
	UpdatedAt   time.Time              `json:"updatedAt"`
	DeletedAt   gorm.DeletedAt         `json:"-" gorm:"index"`
}

// TableName 指定表名
func (DataProxy) TableName() string {
	return "data_proxies"
}

// BeforeCreate GORM钩子：创建前
func (p *DataProxy) BeforeCreate(tx *gorm.DB) error {
	if p.Config == nil {
		p.Config = make(map[string]interface{})
	}
	return p.validateConfig()
}

// BeforeUpdate GORM钩子：更新前
func (p *DataProxy) BeforeUpdate(tx *gorm.DB) error {
	return p.validateConfig()
}

// validateConfig 验证配置
func (p *DataProxy) validateConfig() error {
	switch p.Type {
	case ProxyTypeMySQL, ProxyTypeDM:
		return p.validateDatabaseConfig()
	case ProxyTypeHTTP:
		return p.validateHTTPConfig()
	default:
		return fmt.Errorf("unsupported proxy type: %s", p.Type)
	}
}

// validateDatabaseConfig 验证数据库配置
func (p *DataProxy) validateDatabaseConfig() error {
	if _, ok := p.Config["dsn"]; !ok {
		return ErrMissingDSN
	}
	if _, ok := p.Config["table"]; !ok {
		return ErrMissingTable
	}
	return nil
}

// validateHTTPConfig 验证HTTP配置
func (p *DataProxy) validateHTTPConfig() error {
	if _, ok := p.Config["url"]; !ok {
		return ErrMissingURL
	}
	if _, ok := p.Config["method"]; !ok {
		p.Config["method"] = "POST" // 默认POST方法
	}
	return nil
}

// GetDSN 获取数据库连接字符串
func (p *DataProxy) GetDSN() string {
	if dsn, ok := p.Config["dsn"].(string); ok {
		return dsn
	}
	return ""
}

// GetTable 获取目标表名
func (p *DataProxy) GetTable() string {
	if table, ok := p.Config["table"].(string); ok {
		return table
	}
	return ""
}

// GetURL 获取HTTP URL
func (p *DataProxy) GetURL() string {
	if url, ok := p.Config["url"].(string); ok {
		return url
	}
	return ""
}

// GetMethod 获取HTTP方法
func (p *DataProxy) GetMethod() string {
	if method, ok := p.Config["method"].(string); ok {
		return method
	}
	return "POST"
}

// GetHeaders 获取HTTP头
func (p *DataProxy) GetHeaders() map[string]string {
	if headers, ok := p.Config["headers"].(map[string]interface{}); ok {
		result := make(map[string]string)
		for k, v := range headers {
			if str, ok := v.(string); ok {
				result[k] = str
			}
		}
		return result
	}
	return make(map[string]string)
}

// GetTimeout 获取超时时间（毫秒）
func (p *DataProxy) GetTimeout() int {
	if timeout, ok := p.Config["timeout"].(float64); ok {
		return int(timeout)
	}
	return 10000 // 默认10秒
}

// GetMaxOpenConns 获取最大连接数
func (p *DataProxy) GetMaxOpenConns() int {
	if conns, ok := p.Config["maxOpenConns"].(float64); ok {
		return int(conns)
	}
	return 10 // 默认10个连接
}

// GetMaxIdleConns 获取最大空闲连接数
func (p *DataProxy) GetMaxIdleConns() int {
	if conns, ok := p.Config["maxIdleConns"].(float64); ok {
		return int(conns)
	}
	return 5 // 默认5个空闲连接
}

// GetConnMaxLifetime 获取连接最大生命周期（秒）
func (p *DataProxy) GetConnMaxLifetime() int {
	if lifetime, ok := p.Config["connMaxLifetime"].(float64); ok {
		return int(lifetime)
	}
	return 3600 // 默认1小时
}

// IsMySQL 判断是否为MySQL代理
func (p *DataProxy) IsMySQL() bool {
	return p.Type == ProxyTypeMySQL
}

// IsDM 判断是否为达梦代理
func (p *DataProxy) IsDM() bool {
	return p.Type == ProxyTypeDM
}

// IsHTTP 判断是否为HTTP代理
func (p *DataProxy) IsHTTP() bool {
	return p.Type == ProxyTypeHTTP
}

// ToMap 转换为map（用于API响应）
func (p *DataProxy) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":          p.ID,
		"type":        p.Type,
		"name":        p.Name,
		"enabled":     p.Enabled,
		"description": p.Description,
		"config":      p.Config,
		"createdAt":   p.CreatedAt,
		"updatedAt":   p.UpdatedAt,
	}
}

// Clone 克隆代理配置
func (p *DataProxy) Clone() *DataProxy {
	clone := *p
	clone.ID = 0
	clone.CreatedAt = time.Time{}
	clone.UpdatedAt = time.Time{}

	// 深拷贝配置
	if p.Config != nil {
		clone.Config = make(map[string]interface{})
		configBytes, _ := json.Marshal(p.Config)
		json.Unmarshal(configBytes, &clone.Config)
	}

	return &clone
}
