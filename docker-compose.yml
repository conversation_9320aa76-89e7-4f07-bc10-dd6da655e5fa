version: '3.8'

services:
  # MessageWeaver 主服务
  messageweaver:
    build: .
    ports:
      - "8080:8080"
    environment:
      - MW_DATABASE_DSN=root:password@tcp(mysql:3306)/messageweaver?charset=utf8mb4&parseTime=True&loc=Local
      - MW_RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
    depends_on:
      - mysql
      - rabbitmq
    restart: unless-stopped
    networks:
      - messageweaver-network

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: messageweaver
      MYSQL_USER: messageweaver
      MYSQL_PASSWORD: messageweaver
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - messageweaver-network

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RA<PERSON><PERSON><PERSON><PERSON>_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
      - messageweaver-network

  # Redis (可选，用于缓存)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - messageweaver-network

volumes:
  mysql_data:
  rabbitmq_data:
  redis_data:

networks:
  messageweaver-network:
    driver: bridge
