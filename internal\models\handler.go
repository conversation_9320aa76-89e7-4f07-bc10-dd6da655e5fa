package models

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// MessageHandler 消息处理器模型
type MessageHandler struct {
	ID             uint                   `json:"id" gorm:"primaryKey"`
	Name           string                 `json:"name" gorm:"uniqueIndex;not null" validate:"required,min=1,max=255"`
	MqNames        StringArray            `json:"mqNames" gorm:"type:json;not null" validate:"required,min=1"`
	Code           string                 `json:"code" gorm:"type:longtext;not null" validate:"required,min=1"`
	Enabled        bool                   `json:"enabled" gorm:"default:true"`
	Description    string                 `json:"description" gorm:"type:text"`
	DataProxyNames StringArray            `json:"dataProxyNames" gorm:"type:json;not null" validate:"required,min=1"`
	Version        int                    `json:"version" gorm:"default:1"`
	Author         string                 `json:"author" gorm:"size:255"`
	Config         map[string]interface{} `json:"config" gorm:"type:json"`
	CreatedAt      time.Time              `json:"createdAt"`
	UpdatedAt      time.Time              `json:"updatedAt"`
	DeletedAt      gorm.DeletedAt         `json:"-" gorm:"index"`
}

// StringArray 字符串数组类型，用于GORM JSON序列化
type StringArray []string

// Scan 实现sql.Scanner接口
func (s *StringArray) Scan(value interface{}) error {
	if value == nil {
		*s = nil
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, s)
	case string:
		return json.Unmarshal([]byte(v), s)
	}

	return nil
}

// Value 实现driver.Valuer接口
func (s StringArray) Value() (interface{}, error) {
	if s == nil {
		return nil, nil
	}
	return json.Marshal(s)
}

// TableName 指定表名
func (MessageHandler) TableName() string {
	return "message_handlers"
}

// BeforeCreate GORM钩子：创建前
func (h *MessageHandler) BeforeCreate(tx *gorm.DB) error {
	if h.Config == nil {
		h.Config = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (h *MessageHandler) BeforeUpdate(tx *gorm.DB) error {
	h.Version++
	return nil
}

// GetTimeout 获取超时配置
func (h *MessageHandler) GetTimeout() int {
	if timeout, ok := h.Config["timeout"].(float64); ok {
		return int(timeout)
	}
	return 5000 // 默认5秒
}

// GetMemoryLimit 获取内存限制配置
func (h *MessageHandler) GetMemoryLimit() string {
	if limit, ok := h.Config["memoryLimit"].(string); ok {
		return limit
	}
	return "64MB" // 默认64MB
}

// IsValidForMQ 检查是否处理指定MQ队列
func (h *MessageHandler) IsValidForMQ(mqName string) bool {
	for _, name := range h.MqNames {
		if name == mqName {
			return true
		}
	}
	return false
}

// GetDataProxyNames 获取数据代理名称列表
func (h *MessageHandler) GetDataProxyNames() []string {
	return []string(h.DataProxyNames)
}

// SetMqNames 设置MQ队列名称
func (h *MessageHandler) SetMqNames(names []string) {
	h.MqNames = StringArray(names)
}

// SetDataProxyNames 设置数据代理名称
func (h *MessageHandler) SetDataProxyNames(names []string) {
	h.DataProxyNames = StringArray(names)
}

// Validate 验证处理器配置
func (h *MessageHandler) Validate() error {
	if h.Name == "" {
		return ErrInvalidHandlerName
	}
	if len(h.MqNames) == 0 {
		return ErrEmptyMqNames
	}
	if h.Code == "" {
		return ErrEmptyCode
	}
	if len(h.DataProxyNames) == 0 {
		return ErrEmptyDataProxyNames
	}
	return nil
}

// Clone 克隆处理器（用于版本管理）
func (h *MessageHandler) Clone() *MessageHandler {
	clone := *h
	clone.ID = 0
	clone.Version = h.Version + 1
	clone.CreatedAt = time.Time{}
	clone.UpdatedAt = time.Time{}

	// 深拷贝切片
	clone.MqNames = make(StringArray, len(h.MqNames))
	copy(clone.MqNames, h.MqNames)

	clone.DataProxyNames = make(StringArray, len(h.DataProxyNames))
	copy(clone.DataProxyNames, h.DataProxyNames)

	// 深拷贝配置
	if h.Config != nil {
		clone.Config = make(map[string]interface{})
		for k, v := range h.Config {
			clone.Config[k] = v
		}
	}

	return &clone
}

// ToMap 转换为map（用于API响应）
func (h *MessageHandler) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"id":             h.ID,
		"name":           h.Name,
		"mqNames":        h.MqNames,
		"code":           h.Code,
		"enabled":        h.Enabled,
		"description":    h.Description,
		"dataProxyNames": h.DataProxyNames,
		"version":        h.Version,
		"author":         h.Author,
		"config":         h.Config,
		"createdAt":      h.CreatedAt,
		"updatedAt":      h.UpdatedAt,
	}
}
