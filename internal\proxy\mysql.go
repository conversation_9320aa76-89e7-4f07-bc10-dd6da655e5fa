package proxy

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"messageweaver/internal/logger"
	"messageweaver/internal/models"

	_ "github.com/go-sql-driver/mysql"
	"go.uber.org/zap"
)

// MySQLProxy MySQL数据代理
type MySQLProxy struct {
	config  *models.DataProxy
	logger  logger.Logger
	db      *sql.DB
	enabled bool
}

// NewMySQLProxy 创建MySQL代理
func NewMySQLProxy(config *models.DataProxy, logger logger.Logger) (*MySQLProxy, error) {
	if config.Type != models.ProxyTypeMySQL {
		return nil, fmt.Errorf("invalid proxy type: %s", config.Type)
	}

	proxy := &MySQLProxy{
		config:  config,
		logger:  logger,
		enabled: config.Enabled,
	}

	// 建立数据库连接
	if err := proxy.connect(); err != nil {
		return nil, err
	}

	return proxy, nil
}

// connect 建立数据库连接
func (p *MySQLProxy) connect() error {
	dsn := p.config.GetDSN()
	if dsn == "" {
		return fmt.Errorf("missing DSN in config")
	}

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open database: %w", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(p.config.GetMaxOpenConns())
	db.SetMaxIdleConns(p.config.GetMaxIdleConns())
	db.SetConnMaxLifetime(time.Duration(p.config.GetConnMaxLifetime()) * time.Second)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return fmt.Errorf("failed to ping database: %w", err)
	}

	p.db = db
	return nil
}

// Send 发送数据
func (p *MySQLProxy) Send(data interface{}) error {
	if !p.enabled {
		return fmt.Errorf("proxy is disabled")
	}

	if p.db == nil {
		return fmt.Errorf("database connection not established")
	}

	table := p.config.GetTable()
	if table == "" {
		return fmt.Errorf("missing table in config")
	}

	// 转换数据为map
	dataMap, err := p.convertToMap(data)
	if err != nil {
		return fmt.Errorf("failed to convert data: %w", err)
	}

	// 构建INSERT语句
	query, args, err := p.buildInsertQuery(table, dataMap)
	if err != nil {
		return fmt.Errorf("failed to build query: %w", err)
	}

	// 执行插入
	result, err := p.db.Exec(query, args...)
	if err != nil {
		p.logger.Error("Failed to execute insert",
			zap.String("proxy", p.config.Name),
			zap.String("table", table),
			zap.String("query", query),
			zap.Error(err),
		)
		return fmt.Errorf("failed to execute insert: %w", err)
	}

	// 记录成功日志
	rowsAffected, _ := result.RowsAffected()
	p.logger.Debug("Data inserted successfully",
		zap.String("proxy", p.config.Name),
		zap.String("table", table),
		zap.Int64("rows_affected", rowsAffected),
	)

	return nil
}

// convertToMap 转换数据为map
func (p *MySQLProxy) convertToMap(data interface{}) (map[string]interface{}, error) {
	switch v := data.(type) {
	case map[string]interface{}:
		return v, nil
	case []byte:
		var result map[string]interface{}
		if err := json.Unmarshal(v, &result); err != nil {
			return nil, err
		}
		return result, nil
	case string:
		var result map[string]interface{}
		if err := json.Unmarshal([]byte(v), &result); err != nil {
			return nil, err
		}
		return result, nil
	default:
		// 使用反射转换结构体
		return p.structToMap(data)
	}
}

// structToMap 结构体转map
func (p *MySQLProxy) structToMap(data interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	
	v := reflect.ValueOf(data)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	
	if v.Kind() != reflect.Struct {
		return nil, fmt.Errorf("unsupported data type: %T", data)
	}
	
	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)
		
		// 跳过未导出的字段
		if !value.CanInterface() {
			continue
		}
		
		// 获取字段名（优先使用json tag）
		fieldName := field.Name
		if jsonTag := field.Tag.Get("json"); jsonTag != "" && jsonTag != "-" {
			if commaIdx := strings.Index(jsonTag, ","); commaIdx > 0 {
				fieldName = jsonTag[:commaIdx]
			} else {
				fieldName = jsonTag
			}
		}
		
		result[fieldName] = value.Interface()
	}
	
	return result, nil
}

// buildInsertQuery 构建INSERT查询
func (p *MySQLProxy) buildInsertQuery(table string, data map[string]interface{}) (string, []interface{}, error) {
	if len(data) == 0 {
		return "", nil, fmt.Errorf("no data to insert")
	}

	var columns []string
	var placeholders []string
	var args []interface{}

	for column, value := range data {
		columns = append(columns, "`"+column+"`")
		placeholders = append(placeholders, "?")
		args = append(args, value)
	}

	query := fmt.Sprintf(
		"INSERT INTO `%s` (%s) VALUES (%s)",
		table,
		strings.Join(columns, ", "),
		strings.Join(placeholders, ", "),
	)

	return query, args, nil
}

// GetName 获取代理名称
func (p *MySQLProxy) GetName() string {
	return p.config.Name
}

// GetType 获取代理类型
func (p *MySQLProxy) GetType() models.DataProxyType {
	return p.config.Type
}

// IsEnabled 是否启用
func (p *MySQLProxy) IsEnabled() bool {
	return p.enabled
}

// TestConnection 测试连接
func (p *MySQLProxy) TestConnection() error {
	if p.db == nil {
		return fmt.Errorf("database connection not established")
	}
	return p.db.Ping()
}

// Close 关闭连接
func (p *MySQLProxy) Close() error {
	if p.db != nil {
		return p.db.Close()
	}
	return nil
}

// GetStatus 获取状态
func (p *MySQLProxy) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"name":    p.config.Name,
		"type":    p.config.Type,
		"enabled": p.enabled,
		"table":   p.config.GetTable(),
	}

	if p.db != nil {
		stats := p.db.Stats()
		status["connection_stats"] = map[string]interface{}{
			"max_open_connections": stats.MaxOpenConnections,
			"open_connections":     stats.OpenConnections,
			"in_use":              stats.InUse,
			"idle":                stats.Idle,
		}

		// 测试连接
		if err := p.db.Ping(); err != nil {
			status["connection_error"] = err.Error()
			status["healthy"] = false
		} else {
			status["healthy"] = true
		}
	} else {
		status["healthy"] = false
		status["connection_error"] = "no database connection"
	}

	return status
}

// SetEnabled 设置启用状态
func (p *MySQLProxy) SetEnabled(enabled bool) {
	p.enabled = enabled
}

// ExecuteQuery 执行自定义查询（用于测试）
func (p *MySQLProxy) ExecuteQuery(query string, args ...interface{}) ([]map[string]interface{}, error) {
	if p.db == nil {
		return nil, fmt.Errorf("database connection not established")
	}

	rows, err := p.db.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	var results []map[string]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			row[col] = values[i]
		}
		results = append(results, row)
	}

	return results, rows.Err()
}
